<template>
  <div class="finance-stagement mt10">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <ll-search-form :model="form" :form-items="formItems" :has-reset="false" @submit="handleSubmit" />
      <p>
        <ll-button @click="showDialog('editVisible', null)">添加</ll-button>
      </p>
      <ll-data-grid ref="dataTable" is-column-index :table-columns="tableConf.tableColumns" :async-fetch-data-fun="fetchData">
        <template slot="table-column-active" slot-scope="scope">
          <el-switch
            v-model="scope.row.active"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="0"
            :inactive-value="1"
            @change="
              e => {
                isActiveChange(e, scope.row)
              }
            "
          />
        </template>
        <template slot="table-column-operation" slot-scope="scope">
          <div>
            <el-button type="text" @click="showDialog('dialogVisible', scope.row)">权限设置</el-button>
            <el-button type="text" @click="showDialog('editVisible', scope.row)">编辑</el-button>
            <el-button type="text" @click="deleteRow(scope.row)">删除</el-button>
          </div>
        </template>
      </ll-data-grid>
      <el-dialog title="权限设置" :visible.sync="dialogVisible" width="30%">
        <el-form ref="modelForm" :model="modelForm" label-width="80px">
          <el-form-item label="数据权限">
            <el-checkbox-group v-model="modelForm.dataPermission">
              <el-checkbox label="YKQ">宜块钱</el-checkbox>
              <el-checkbox label="POP">pop</el-checkbox>
              <el-checkbox label="ZHL">智慧脸商城</el-checkbox>
              <el-checkbox label="ec">药帮忙</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="菜单权限">
            <el-tree ref="tree" :data="menuPermission" show-checkbox node-key="id" :default-expanded-keys="[2, 3]" :props="defaultProps"
          /></el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="updatePower">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog :title="editFormTitle" :visible.sync="editVisible" width="30%">
        <el-form ref="editForm" :model="editForm" label-width="80px">
          <el-form-item label="账号">
            <el-input v-model="editForm.userAccount" :disabled="isEdit" />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="editForm.userName" :disabled="isEdit" />
          </el-form-item>
          <el-form-item label="所属部门">
            <el-select v-model="editForm.depart" placeholder="请选择部门">
              <el-option label="财务部" value="财务部" />
              <el-option label="业务部" value="业务部" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否启用">
            <el-switch v-model="editForm.active" active-color="#13ce66" inactive-color="#ff4949" :active-value="0" :inactive-value="1" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="editVisible = false">取 消</el-button>
          <el-button type="primary" @click="updateUser">确 定</el-button>
        </span>
      </el-dialog>
    </ll-list-page-layout>
  </div>
</template>
<script>
import { getUserPower, updatePermission, updateUser, getUserList, addUser, getAllMenu } from '@/api/account.js'
export default {
  data() {
    return {
      form: {
        userName: '',
        userAccount: '',
        depart: ''
      },
      formItems: [
        {
          label: '账号',
          prop: 'userAccount',
          component: 'el-input',
          attrs: {
            placeholder: '账号'
          }
        },
        {
          label: '姓名',
          prop: 'userName',
          component: 'el-input',
          attrs: {
            placeholder: '姓名'
          }
        },
        {
          label: '所属部门',
          prop: 'depart',
          component: 'll-select',
          attrs: {
            options: [
              {
                value: '',
                label: '全部'
              },
              {
                value: '财务部',
                label: '财务部'
              },
              {
                value: '业务部',
                label: '业务部'
              }
            ]
          }
        }
      ],
      tableConf: {
        ref: 'accountTable',
        tableColumns: [
          {
            label: '账号',
            prop: 'userAccount',
            align: 'center',
            width: 261
          },
          {
            label: '姓名',
            prop: 'userName',
            align: 'center',
            width: 261
          },
          {
            label: '所属部门',
            prop: 'depart',
            align: 'center',
            width: 261
          },
          {
            label: '添加时间',
            prop: 'createTime',
            align: 'center',
            width: 220,
            bizModel: [2, 3]
          },
          {
            label: '最后登录时间',
            prop: 'lastLoginTime',
            align: 'center',
            width: 220
          },
          {
            slotName: 'active',
            label: '是否启用',
            prop: 'active',
            align: 'center',
            width: 220
          },
          {
            slotName: 'operation',
            label: '操作',
            width: '160',
            fixed: 'right'
          }
        ]
      },
      fullscreenLoading: false,
      dialogVisible: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      editVisible: false,
      modelForm: {
        account: '',
        dataPermission: [],
        // dataPermission: ['YKQ','POP','ZHL'],
        menuLine: ''
      },
      menuPermission: [
        // {
        //   id: '10',
        //   label: '财务管理',
        //   children: [
        //     { id: '10001', label: '财务报表' },
        //     { id: '10008', label: '富民子账户对账' },
        //     { id: '10005', label: '提现' }
        //   ]
        // },
        // {
        //   id: '2',
        //   label: '账号权限'
        // }
      ],
      editForm: {
        userAccount: '',
        userName: '',
        depart: '财务部',
        active: 1
      },
      editFormTitle: '账号编辑',
      currentRow: {},
      switchValue: '1',
      isEdit: false,
      msg: '',
      selectNode: []
    }
  },
  computed: {
    name() {
      return this.$store.getters.name
    }
  },
  created() {
    this.accountQuery()
  },
  methods: {
    // 获取用户信息
    async accountQuery() {
      try {
        let {result} = await getAllMenu()
        this.menuPermission = result.menuPermission || []
      } catch (error) {
        console.log(error)
      }
    },
    fetchData({ pagination }) {
      const { pageSize, currentPage } = pagination
      const params = Object.assign({ pageSize: pageSize, page: currentPage }, this.form)
      return new Promise(resolve => {
        getUserList(params)
          .then(res => {
            console.log(res)
            let result = {
              list: res.data,
              pagination: {
                pageSize: res.pageSize,
                total: res.totalCount
              }
            }
            resolve(result)
          })
          .catch(err => {
            console.log(err)
          })
      })
    },
    handleSubmit(data) {
      this.form = data
      this.$refs.dataTable.loadData()
    },
    handleSizeChange(val) {
      this.tableConf.pagination.pageSize = val
      this.tableConf.pagination.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.tableConf.pagination.currentPage = val
      this.getList()
    },
    showDialog(visiable, row) {
      this.currentRow = row
      if (visiable == 'dialogVisible') {
        //权限设置，调用权限接口
        // 置空
        this.dialogVisible = true
        this.$nextTick(()=>{
          this.$refs.tree.setCheckedKeys([])
        })
        this.modelForm.dataPermission = []
        getUserPower(row.userAccount)
          .then(res => {
            let { result } = res
            this.modelForm.account = result.account
            this.modelForm.dataPermission = result.dataPermission ? result.dataPermission.split(',') : []
            this.selectNode = result.menuLine ? result.menuLine.split(',') : []
            this.$refs.tree.setCheckedKeys(this.selectNode)
          })
          .catch(err => {
            console.log(err)
          })
      } else {
        //编辑
        if (this.currentRow) {
          this.editFormTitle = '账号编辑'
          this.isEdit = true
          for (let i in this.editForm) {
            this.editForm[i] = this.currentRow[i]
          }
        } else {
          this.isEdit = false
          this.editFormTitle = '账号添加'
          //添加权限账号
          this.resetEditForm()
        }
      }
      this[visiable] = true
    },
    resetEditForm() {
      this.editForm = {
        userAccount: '',
        userName: '',
        depart: '财务部',
        active: 1
      }
    },
    deleteRow(row) {
      const { userName, depart, active, userAccount } = row
      this.msg = '账号删除成功！'
      this.editAccount({ userName, depart, active, isDelete: 1, userAccount })
    },
    isActiveChange(e, row) {
      const { userName, depart, userAccount } = row
      this.msg = '账号信息提交成功！'
      this.editAccount({ userName, depart, active: e, isDelete: 0, userAccount })
    },
    editAccount(data) {
      updateUser(data)
        .then(res => {
          console.log(res)
          this.$message({
            message: this.msg,
            center: true,
            type: 'success'
          })
          this.$refs.dataTable.loadData()
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 权限设置
    updatePower() {
      let halfKeys = this.$refs.tree.getHalfCheckedKeys()
      let nodeKeys = this.$refs.tree.getCheckedKeys()
      this.modelForm.menuLine = nodeKeys.join(',')
      this.dialogVisible = false
      let dataPermission = this.modelForm.dataPermission.join(',')
      updatePermission({ ...this.modelForm, dataPermission,  harf: halfKeys.join(',')})
        .then(async res => {
          console.log(res)
          this.$message({
            message: '权限设置成功！',
            center: true,
            type: 'success'
          })
          let userInfo = JSON.parse(localStorage.getItem('userInfo')) 
          userInfo.dataPermission = dataPermission
          localStorage.setItem('userInfo', userInfo)
          this.$store.commit('user/SET_DATAPERMISSION', dataPermission || '')
          location.reload();
        })
        .catch(err => {
          console.log(err)
        })
    },
    updateUser() {
      const data = { ...this.editForm }
      if (this.isEdit) {
        data.isDelete = 0
        this.msg = '账号信息提交成功！'
        this.editAccount(data)
      } else {
        addUser(data)
          .then(res => {
            console.log(res)
            this.$message({
              message: '账号信息提交成功！',
              center: true,
              type: 'success'
            })
            this.$refs.dataTable.loadData()
          })
          .catch(err => {
            console.log(err)
          })
      }
      this.editVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.finance-stagement {
  height: calc(100% - 10px);
  .statement-search-form {
    .el-form-item {
      width: 340px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
</style>
