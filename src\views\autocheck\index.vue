<template>
  <div class="finance-stagement mt10" style="">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="实际收款日期" label-width="100px" prop="receiptTime">
          <el-date-picker
            v-model="formModel.receiptTime"
            type="daterange"
            :clearable="true"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="formItem[0].attrs['picker-options']"
            @change="validateFormRequiredItem"
          />
        </el-form-item>

        <el-form-item label="业务场景" label-width="100px" prop="businessOrderType">
          <ll-search-form-select v-model="formModel.businessOrderType" :options="formItem[1].attrs['options']" />
        </el-form-item>

        <el-form-item label="商户号" label-width="100px" prop="mchId">
          <el-input v-model="formModel.mchId" placeholder="商户号" class="samewidth" />
        </el-form-item>

        <el-form-item label="类型" label-width="100px" prop="orderType">
          <ll-search-form-select v-model="formModel.orderType" :options="formItem[2].attrs['options']" />
        </el-form-item>

        <el-form-item label="商户名称" label-width="100px" prop="businessUserName">
          <el-input v-model="formModel.businessUserName" placeholder="商户名称" class="samewidth" />
        </el-form-item>

        <el-form-item label="订单号" label-width="100px" prop="businessNo">
          <el-input v-model="formModel.businessNo" placeholder="订单号" class="samewidth" />
        </el-form-item>

        <el-form-item label="支付流水号" label-width="100px" prop="payNo">
          <el-input v-model="formModel.payNo" placeholder="支付流水号" class="samewidth" />
        </el-form-item>

        <el-form-item label="支付方式" label-width="100px" prop="payType">
          <ll-search-form-select v-model="formModel.payType" :options="formItem[3].attrs['options']" />
        </el-form-item>

        <el-form-item label="对账状态" label-width="100px" prop="status">
          <ll-search-form-select v-model="formModel.status" :options="formItem[4].attrs['options']" />
        </el-form-item>

        <el-form-item label=" " label-width="100px">
          <ll-button type="primary" @click="handleFormSubmit">查询</ll-button>
          <ll-button type="text" @click="handleFormReset">重置</ll-button>
        </el-form-item>
      </el-form>

      <!-- 主要操作 -->
      <template slot="action-bar_left">
        <el-button @click="handleExportFile">导出Excel</el-button>
      </template>
      <!-- <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
      /> -->
      <ll-data-grid
        ref="dataGrid"
        :async-fetch-data-fun="fetchData"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
      >
        <el-table-column>
          <el-table-column prop="serialNumber" label="序号" width="120" />
        </el-table-column>
        <el-table-column label="第三方支付" class-name="bgblue">
          <el-table-column prop="payTime" label="实际收款日期" width="120" />
          <el-table-column prop="mchId" label="商户号" width="120" />
          <el-table-column prop="channelOrderNo" label="渠道商户订单号" width="120" />
          <el-table-column prop="goodsSubject" label="商品名称" width="120" />
          <el-table-column prop="type" label="类型" width="120" />
          <el-table-column prop="channelOrderAmount" label="渠道订单金额" width="120" />
          <el-table-column prop="channelServiceCharge" label="渠道手续费" width="120" />
        </el-table-column>
        <el-table-column label="业务侧" class-name="bggreen">
          <el-table-column prop="businessUserId" label="商户ID" width="120" />
          <el-table-column prop="businessUserName" label="商户名称" width="120" />
          <el-table-column prop="businessNo" label="订单号" width="120" />
          <el-table-column prop="payNo" label="支付流水号" width="120" />
          <el-table-column prop="originPayNo" label="原支付流水号" width="120" />
          <el-table-column prop="payType" label="支付方式" width="120" />
          <el-table-column prop="productAmount" label="商品金额" width="120" />
          <el-table-column prop="freight" label="运费金额" width="120" />
          <el-table-column prop="orderAmount" label="订单金额" width="120" />
          <el-table-column prop="storeDiscount" label="店铺优惠" width="120" />
          <el-table-column prop="platformDiscount" label="平台优惠" width="120" />
          <el-table-column prop="realAmount" label="实收金额" width="120" />
        </el-table-column>
        <el-table-column>
          <el-table-column prop="status" label="对账状态" width="120" />
        </el-table-column>
      </ll-data-grid>
    </ll-list-page-layout>
  </div>
</template>

<script>
import { autoCheckBill, reportAutoCheck } from '@/api/report'
import moment from 'moment'
import { parseTime, download } from '@/utils/index.js'
const dataOption = [
  { desc: '宜块钱', label: '宜块钱', value: 'YKQ' },
  { desc: 'POP', label: 'POP', value: 'POP' },
  { desc: '智慧脸商城', label: '智慧脸商城', value: 'ZHL' },
  { desc: '药帮忙', label: '药帮忙', value: 'ec' }
]
export default {
  data() {
    return {
      searchFormKey: `search-form-key-${new Date().getTime()}`,
      formModel: {
        businessOrderType: undefined, // 业务场景
        mchId: null, //商户号
        orderType: undefined, // 类型
        businessUserName: null, //商户名称
        payNo: null, //支付流水号
        businessNo: null, //订单号
        payType: undefined, //支付方式
        status: undefined, //对账状态
        receiptTime: [
          moment()
            // .subtract(1, 'months')
            .startOf('month')
            .startOf('hour')
            .valueOf(),
          moment()
            .subtract(1, 'days')
            .endOf('month' + 1)
            .startOf('day')
            .startOf('hour')
            .valueOf()
        ] // 交易时间
      },
      formItem: [
        {
          label: '交易时间',
          prop: 'receiptTime',
          component: 'el-date-picker',
          attrs: {
            type: 'daterange',
            clearable: 'true',
            'range-separator': '至',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            'picker-options': {
              disabledDate: this.receiptTimeDisabledDate,
              onPick: this.receiptTimeOnPick
            }
          },
          width: '220px',
          error: ''
        },
        {
          label: '业务场景',
          prop: 'businessOrderType',
          component: 'll-select',
          attrs: {
            options: [
              { desc: '宜块钱', label: '宜块钱', value: 'YKQ' },
              { desc: 'POP', label: 'POP', value: 'POP' },
              { desc: '智慧脸商城', label: '智慧脸商城', value: 'ZHL' }
            ]
          },
          width: '220px'
        },
        {
          label: '类型',
          prop: 'orderType',
          component: 'll-select',
          attrs: {
            clearable: false,
            noAddAll: true,
            options: [
              { value: 'pay', desc: '收款', label: '收款' },
              { value: 'refund', desc: '退款', label: '退款' }
            ]
          },
          width: '220px'
        },
        {
          label: '支付方式',
          prop: 'payType',
          component: 'll-select',
          attrs: {
            clearable: false,
            noAddAll: true,
            options: [
              { value: 'wx', desc: '微信', label: '微信' },
              { value: 'alipay', desc: '支付宝', label: '支付宝' }
            ]
          },
          width: '220px'
        },
        {
          label: '对账状态',
          prop: 'status',
          component: 'll-select',
          attrs: {
            clearable: false,
            noAddAll: true,
            options: [
              { value: 'success', desc: '对账成功', label: '对账成功' },
              { value: 'fail', desc: '对账失败', label: '对账失败' }
            ]
          },
          width: '220px'
        }
      ],
      formRules: {
        receiptTime: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ],
        mchId: [
          {
            type: 'string',
            max: 60,
            message: '商户号长度在60字符以内',
            trigger: 'blur'
          }
        ],
        businessUserName: [
          {
            type: 'string',
            max: 60,
            message: '商户名称长度在60字符以内',
            trigger: 'blur'
          }
        ]
      },
      receiptTimeOptionRange: '',
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false
    }
  },
  created() {
    this.initDate()
    this.accountQuery()
  },
  methods: {
    // 初始化实际收款日期
    initDate() {
      // 如果今天是1号 那么时间变成上个月整月
      if (moment().date() === 1) {
        this.formModel.receiptTime = [
          moment()
            .subtract(1, 'months')
            .startOf('month')
            .startOf('hour')
            .valueOf(),
          moment()
            .subtract(1, 'months')
            .endOf('month')
            .startOf('day')
            .startOf('hour')
            .valueOf()
        ]
        console.log(this.formModel.receiptTime)
      }
    },
    // 重置
    handleFormReset() {
      this.$refs.searchForm.resetFields()
      this.initDate()
      this.handleFormSubmit()
    },
    // 获取用户信息
    accountQuery() {
      let option = []
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      dataOption.forEach(p => {
        if (dataPermission.includes(p.value)) {
          option.push(p)
        }
      })
      // if (dataPermission.includes('YKQ')) {
      //   this.formModel.businessOrderType = 'YKQ'
      // }
      this.formItem[1].attrs.options = option
      if (option.length === 1) {
        this.formModel.businessOrderType = option[0].value
      }
    },
    validateFormRequiredItem() {
      this.$refs.searchForm.validateField('receiptTime')
      this.$refs.searchForm.validateField('tradeNo')
      this.$refs.searchForm.validateField('businessOrderNo')
      this.$refs.searchForm.validateField('channelTransactionNo')
    },
    /**
     * 校验 交易时间、商户订单号、业务订单号、支付渠道交易单号，至少一项填写
     */
    validateRequiredItem(rule, value, callback) {
      let errorMsg = ''
      if (this.validateParamsAllEmpty()) {
        errorMsg = '请填写任意一项筛选项'
        callback(new Error(errorMsg))
      }
      callback()
    },

    validateParamsAllEmpty() {
      const {
        // 交易时间
        receiptTime
      } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      if (this.xyySaasValidator.isNull(beginDate) && this.xyySaasValidator.isNull(endDate)) {
        return false
      }
    },
    formatterTableData(tableData, pagination, totalCount) {
      console.log(tableData)
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
    getFormParams(pagination) {
      const { receiptTime } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      let params = {
        businessType: this.formModel.businessOrderType, //业务场景
        mchId: this.formModel.mchId, //商户号
        type: this.formModel.orderType, //类型
        businessUserName: this.formModel.businessUserName, //商户名称
        businessNo: this.formModel.businessNo, //订单号
        payNo: this.formModel.payNo, //支付流水号
        payType: this.formModel.payType, //支付方式
        status: this.formModel.status, //对账状态
        payTimeStart: beginDate, // 交易时间 - 开始时间
        payTimeEnd: endDate, // 交易时间 - 结束时间
        page: pagination ? pagination.currentPage : null,
        pageSize: pagination ? pagination.pageSize : null
      }
      console.log(params)
      delete params.receiptTime
      for (const key in params) {
        if (this.xyySaasValidator.isNull(params[key])) {
          delete params[key]
        }
      }

      return params
    },
    fetchData({ pagination }) {
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      if (dataPermission === '') {
        let obj = {
          list: [],
          pagination: { total: 0 }
        }
        return Promise.resolve(obj)
      }
      let params = this.getFormParams(pagination)
      return new Promise(resolve => {
        autoCheckBill(params)
          .then(response => {
            let { data } = response
            let { totalCount } = response
            let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },

    receiptTimeDisabledDate(time) {
      // console.log("receiptTimeDisabledDate", moment(time));
      let receiptTimeOptionRange = this.receiptTimeOptionRange

      if (receiptTimeOptionRange) {
        // let secondNum = 60 * 60 * 24 * 3 * 1000;
        let startTime = receiptTimeOptionRange,
          currTime = moment(time),
          minTime = moment(startTime).subtract(1, 'months'),
          maxTime = moment(startTime).add(1, 'months')
        return currTime.isBefore(minTime) || currTime.isAfter(maxTime)
      } else {
        return false
      }
    },
    receiptTimeOnPick(time) {
      //当第一时间选中才设置禁用
      if (time.minDate && !time.maxDate) {
        this.receiptTimeOptionRange = time.minDate
      }
      if (time.maxDate) {
        this.receiptTimeOptionRange = null
      }
    },

    // 重新加载表
    refreshList() {
      this.$refs['dataGrid'].loadData()
    },

    // 查询
    handleFormSubmit() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.refreshList()
        } else {
          return false
        }
      })
    },

    // 导出excel
    handleExportFile() {
      this.fullscreenLoading = true
      let exportParams = this.getFormParams()
      console.log(exportParams)
      const exportName = '收款自动对账'
      reportAutoCheck(exportParams)
        .then(response => {
          let result = response.data
          const dateStr = parseTime(new Date(), '{y}{m}{d}')
          const defExportName = `${exportName}-${dateStr}.xlsx`
          const fileExportName =
            response && response.headers && response.headers['export-filename']
              ? decodeURIComponent(response.headers['export-filename'])
              : defExportName
          download(result, fileExportName)
        })
        .finally(() => {
          this.fullscreenLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
/deep/ .cell {
  width: 100%;
  text-align: center;
}
/deep/ .bgblue {
  background-color: #338fcc !important;
  color: #ffffff;
}
/deep/ .bggreen {
  background-color: #70ae47 !important;
  color: #ffffff;
}
</style>
