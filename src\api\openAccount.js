import request from '@/utils/request'
import requestDownload from '@/utils/requestDownload'
/**
 * 查询开户状态
 */
export function queryOpenAccountStatus(data) {
  return request({
    url: '/openAccount/queryStatus',
    method: 'post',
    data
  })
}
/**
 * 查询开户信息
 */
export function queryOpenAccountInfo(data) {
  return request({
    url: '/openAccount/queryOpenAccountInfo',
    method: 'post',
    data
  })
}

/**
 * 开户意愿申请单重新申请
 */
export function openAccountApplyment(data) {
  return request({
    url: '/openAccount/openAccountApplyment',
    method: 'post',
    data
  })
}

/**
 * 开通支付功能
 */
export function retryOpenPay(data) {
  return request({
    url: '/openAccount/retryOpenPay',
    method: 'post',
    data
  })
}
/**
 * 更新用户信息
 */
export function updateUserInfo(data) {
  return request({
    url: '/openAccount/update/userinfo',
    method: 'post',
    data
  })
}
/**
 * 平安还款计划 列表查询
 */
export function getProjectList(data) {
  return request({
    url: '/pinganLoan/repaymentPlan/query',
    method: 'post',
    data
  })
}
export function exportProject(data) {
  return requestDownload({
    url: '/pinganLoan/repaymentPlan/export',
    method: 'post',
    data
  })
}
/**结局单详情页内容查询*/
export function getjdDetail(data) {
  return request({
    url: '/jdCredit/bill/detail',
    method: 'post',
    data
  })
}
/**京东金融对账列表页查询*/
export function getjdCredit(data) {
  return request({
    url: '/jdCredit/bill/list',
    method: 'post',
    data
  })
}
/**京东金融对账列表导出 */
export function exportjdList(data) {
  return requestDownload({
    url: '/jdCredit/bill/exportList',
    method: 'post',
    data
  })
}
