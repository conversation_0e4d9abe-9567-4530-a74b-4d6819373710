const path = require("path");
const insertDataToHtml = require('./insertDataToHtml.plugin')
const moment = require('moment');
// const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir);
}
// const mode = process.env.NODE_ENV
// console.log('mode......', mode)
// console.log('targetUrl......', process.env.VUE_APP_URL)

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  lintOnSave: false,
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  // publicPath: '/',
  // outputDir: 'dist',
  // // assetsDir: 'static',
  // lintOnSave: process.env.NODE_ENV === 'development',
  // productionSourceMap: false,
  devServer: {
    // host: "dev.paymanage-new.stage.ybm100.com",
    port: 9527,
    open: true,
    hot: true,
    inline: true,
    https: false,
    overlay: {
      warnings: false,
      errors: true
    },
    // proxy: 'https://payment.test.ybm100.com'
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: process.env.VUE_APP_URL, // 要跨域的域名
        changeOrigin: true, // 是否开启跨域
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: ""
        },
        onProxyReq(proxyReq, req) {
          const { cookie } = req.headers
          if (cookie) {
            proxyReq.setHeader('Origin', 'https://paymanage-new.stage.ybm100.com')
            proxyReq.setHeader(
              'Cookie',
              'sid=aa511992-71ca-49a7-8b05-b48e2240310e; uid=CgoUFGcRzA964xPiBTLcAg==; crosSdkDT2019DeviceId=325fw0-r2sh28-do9ggkqjtuiykca-hbug6wz7f; cna=3103790333a7497f5561bd730c58b346; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; web_did=%7B%22did%22%3A%20%22bda88517-3e41-42da-833c-8d8fc6c9de9d%22%7D; web_8b5e1b0f250a436e8c6af9871354bfba=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; web_info=%7B%22sid%22%3A%201752481199871%2C%22updated%22%3A%201752481199872%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22new-www.test.ybm100.com%22%2C%22landHref%22%3A%20%22https%3A%2F%2Fwww.ybm100.com%2F%22%2C%22cuid%22%3A%20%220%22%7D; _abfpc=c5080be8ec88768aca7838b893ee6dd3e08d7f66_2.0; TGC=eyJhbGciOiJIUzUxMiJ9.ZXlKNmFYQWlPaUpFUlVZaUxDSmhiR2NpT2lKa2FYSWlMQ0psYm1NaU9pSkJNVEk0UTBKRExVaFRNalUySW4wLi4xbFlBdXRlWHNPY0F2UERQYUNGN19RLjF1RGRDQ3lNdjZGMDRHSjEzdDFRcnNybXpySEJzZTY2azcwWXp4aS1lMDZRRXR1XzE0Y2xlNVgtbGtreHM0SkRSYjJaX0h4OXh5d1p3NWp1ZXRCc3c3ZWthYVBpUEVCV1NZek5kalFld0N0Xy0xX1ZubUJpVWxnWjEzVXNaUWNaU2c2anJiZ19lTXJtajZHNGpRSEpEcl9QVFY3UXRNeVZDcUROWUlZbVdjMFRzMWlYdzhkY0lTckEzQUt0Zmp6enRHY1ZpNmIzcnhXRTdvb1pUOXpraHdKOE1OaUd2WU56YTNubGlNQmZXRXVNQUFVZVd4QzV6VDBVNGxfcFRDMGNoeTFBMzZoWWxYaEQ1aG1BT1BoRmJ3LmZoeDVKSWg4dURrbnoxdmVPVUNDRHc=.Fb83Nh8c6kEJqHumlIjp6Y5py9bG82wWjYvAgj4nTXczSmUPQNRZK9n_d1J8k4T4s0BMiXg8vsqTbqn8PSKLQQ; token=token'
            )
          }
        }
      }
    }
    // before: require('./mock/mock-server.js')
  },

  configureWebpack: config => {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    // config.name = defaultSettings.title || '收银台' // page title,
    config.resolve.alias = {
      "@": resolve("src"),
      "element-ui": "@xyy-saas/ui"
    };
    // 给输出文件名加hash
    config.output.filename ='bundle[hash].js' 
    // 给打包后的hmlt加打包日期注释
    let buildDate = moment().format('YYYY.MM.DD HH:mm:ss');
    config.plugins.push(new insertDataToHtml({
      text: `<!--build at ${buildDate}-->`
    }))
    // 生产环境去掉sourcemap
    if (process.env.NODE_ENV === 'production') {
      config.devtool = 'none'
    } else {
      // 为开发环境修改配置...
    }
  },

  chainWebpack: config => {
    // set svg-sprite-loader
    config.module
      .rule("svg")
      .exclude.add(resolve("src/icons"))
      .end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]"
      })
      .end();
    config.module
      .rule('eslint')
      .exclude.add('/src/')
      .end();
    // 修复HMR
    config.resolve.symlinks(true);
    // 打开注释
    config.plugin('html')
    .tap(args => {
        if(process.env.NODE_ENV === 'production') {
            args[0].minify.removeComments = false;
        }
        return args;
    });
    
  },
  lintOnSave: false,
  runtimeCompiler: true
};
