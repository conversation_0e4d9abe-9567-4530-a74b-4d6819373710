/*
 *@functionName: insertDataToHtml
 *@params: option={text}
 *@description: 向html加数据
 *@author: z<PERSON><PERSON>
 *@date: 2020-11-10 14:39:37
*/
class insertDataToHtml {
  constructor(option) {
    this.option = option
  }
  apply (compiler) {
    let { text } = this.option
    compiler.plugin('compilation', (compilation) => {
      compilation.plugin(
        'html-webpack-plugin-before-html-processing',
        (data, cb) => {
          data.html += text
          cb&&cb(null, data)
        }
      )
    })
  }
}
module.exports = insertDataToHtml