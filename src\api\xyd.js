import request from '@/utils/request'
import requestDownload from '@/utils/requestDownload'
import qs from 'qs'

/**小雨点还款计划列表查询*/
export function getjdCredit(data) {
  return request({
    url: '/xydLoan/repayPlan/list',
    method: 'post',
    data
  })
}
/**小雨点还款计划明细查询*/
export function getRepayPlandDetail(data) {
    return request({
      url: '/xydLoan/repayPlan/detail',
      method: 'post',
      params:data
    })
}
/**
 * 导出还款计划
 */
export function exportData(data) {
    return requestDownload({
        url: '/jd/export_0028',
        method: 'post',
        data
    })
}
/**
 * 还款计划-导出
 */
export function exportRepayPlanList(data) {
  return requestDownload({
    url: '/xydLoan/repayPlan/exportList',
    method: 'post',
    data
})
}
/**
 * 专户查询-列表
 */
export function getAccountList(data) {
  return request({
    url: '/xydLoan/account/list',
    method: 'post',
    data
  })
}
/**
 * 专户列表-导出
 */
export function exportAccountList(data) {
  return requestDownload({
    url: '/xydLoan/account/exportList',
    method: 'post',
    data
  })
}