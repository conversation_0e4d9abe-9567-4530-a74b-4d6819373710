export default [
  {
    url: "/menu/currtUserMenu",
    type: "post",
    response: _ => {
      return {
        code: 20000,
        msg: "success",
        result: [
          {
            guid: "0a6460e6035848ba9531245bc6bb7fa2",
            code: "200",
            name: "产品管理",
            icon: "nested",
            url: null,
            frontUrl: "Product#/product#/product/productList",
            level: 1,
            front: 1,
            parentGuid: "",
            access: true,
            elementList: null,
            childrenList: [
              {
                guid: "b2641ddf284d4e0187fd411e0e312b58",
                code: "200100",
                name: "产品列表",
                icon: "",
                url: null,
                frontUrl: "Product#productList#/product/productList",
                level: 2,
                front: 1,
                parentGuid: "0a6460e6035848ba9531245bc6bb7fa2",
                access: true,
                elementList: [
                  {
                    guid: "01a405f8391e40b4bd22d46235d6832b",
                    name: "产品列表",
                    url: "/api/product/getProductInfoList",
                    frontUrl: "ProductList#productList#/product/productList",
                    tabIndex: 1,
                    menuGuid: "b2641ddf284d4e0187fd411e0e312b58",
                    access: true
                  },
                  {
                    guid: "44c15e02d8ce4000aa1d49e34947910f",
                    name: "添加产品",
                    url: "/api/product/addProductInfo",
                    frontUrl: "proLisRoleAdd",
                    tabIndex: 0,
                    menuGuid: "b2641ddf284d4e0187fd411e0e312b58",
                    access: true
                  },
                  {
                    guid: "8f8ea22007294c8fa9d09686d1974f27",
                    name: "编辑产品",
                    url: "/api/product/updateProductInfo",
                    frontUrl: "proLisRoleEdit",
                    tabIndex: 0,
                    menuGuid: "b2641ddf284d4e0187fd411e0e312b58",
                    access: true
                  },
                  {
                    guid: "71f8ca530aba41238f65983fde31c891",
                    name: "查看产品详情",
                    url: "/api/product/getProductInfoByGuid",
                    frontUrl: null,
                    tabIndex: 0,
                    menuGuid: "b2641ddf284d4e0187fd411e0e312b58",
                    access: true
                  }
                ],
                childrenList: null
              },
              {
                guid: "e6b1f0338d4d4e2bb49c61dbc642a860",
                code: "200200",
                name: "产品线配置",
                icon: "",
                url: null,
                frontUrl:
                  "ProductLineList#productLineList#/product/productLineList",
                level: 2,
                front: 1,
                parentGuid: "0a6460e6035848ba9531245bc6bb7fa2",
                access: true,
                elementList: [
                  {
                    guid: "efb83316ac434c54a34bb953b4cb690d",
                    name: "产品线列表",
                    url: "/api/productLine/getProductLineList",
                    frontUrl:
                      "ProductLineList#productLineList#/product/productLineList",
                    tabIndex: 2,
                    menuGuid: "e6b1f0338d4d4e2bb49c61dbc642a860",
                    access: true
                  },
                  {
                    guid: "dc0ad0e5cb9f4be4afcb7ca2a03da80f",
                    name: "添加产品线",
                    url: "/api/productLine/addProductLine",
                    frontUrl: "proLisLineRoleAdd",
                    tabIndex: 0,
                    menuGuid: "e6b1f0338d4d4e2bb49c61dbc642a860",
                    access: true
                  },
                  {
                    guid: "ba0080b204ee4723aa1733e638eb8f8a",
                    name: "编辑产品线",
                    url: "/api/productLine/updateProductLine",
                    frontUrl: "proLisLineRoleEdit",
                    tabIndex: 0,
                    menuGuid: "e6b1f0338d4d4e2bb49c61dbc642a860",
                    access: true
                  },
                  {
                    guid: "e54cea12260e426c8e627f27367c8a67",
                    name: "查询产品线详情",
                    url: "/api/productLine/getProductLineByGuid",
                    frontUrl: null,
                    tabIndex: 0,
                    menuGuid: "e6b1f0338d4d4e2bb49c61dbc642a860",
                    access: true
                  }
                ],
                childrenList: null
              }
            ]
          }
        ]
      };
    }
  }
];
