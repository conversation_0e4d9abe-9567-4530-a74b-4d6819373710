<template>
  <div class="finance-stagement mt10">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form ref="searchForm" slot="search-form" class="statement-search-form" label-width="120px" :inline="true" :model="formModel">
        <el-form-item label="出账账户" label-width="90px" prop="outAccountId">
          <ll-select v-model="formModel.outAccountId" :options="outOptions" @change="handleChangeOut" />
        </el-form-item>

        <el-form-item label="到账账户" label-width="90px" prop="inAccountId">
          <ll-select v-model="formModel.inAccountId" :options="entryOptions" />
        </el-form-item>

        <el-form-item label="状态" label-width="90px" prop="status">
          <ll-select v-model="formModel.status" :options="formItem[1].attrs['options']" />
        </el-form-item>

        <el-form-item label="到账时间" label-width="90px" prop="receiptTime">
          <el-date-picker
            v-model="formModel.time"
            type="daterange"
            :clearable="true"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="validateFormRequiredItem"
          />
        </el-form-item>

        <el-form-item label=" " label-width="90px">
          <ll-button type="primary" @click="handleInitiateTransfer">发起转账</ll-button>
          <ll-button @click="handleResetForm">重置</ll-button>
          <ll-button type="primary" @click="handleGetList">查询</ll-button>
        </el-form-item>
      </el-form>

      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
      />
    </ll-list-page-layout>
    <el-dialog
      title="发起转账"
      :visible.sync="dialogVisible"
      width="60%"
      @close="
        dialogVisible = false
        modelForm = {
          businessIdType: 'ec_pop',
          outAccountId: '',
          inAccountId: '',
          amount: '',
          remark: ''
        }
      "
    >
      <el-form ref="modelForm" :model="modelForm" label-width="80px">
        <el-form-item label="出账账户" label-width="90px">
          <ll-select v-model="modelForm.outAccountId" :options="outOptions" @change="handleChangeOutInDialog" />
        </el-form-item>

        <el-form-item label="到账账户" label-width="90px">
          <ll-select v-model="modelForm.inAccountId" :options="entryOptionsInDialog" />
        </el-form-item>

        <el-form-item label="出账账户余额" label-width="110px">
          {{ account }}
        </el-form-item>

        <el-form-item label="转账金额" label-width="90px">
          <el-input v-model="modelForm.amount" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" />
        </el-form-item>

        <el-form-item label="备注" label-width="90px">
          <el-input maxlength="200" v-model="modelForm.remark" type="textarea" autosize />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            dialogVisible = false
            modelForm = {
              businessIdType: 'ec_pop',
              outAccountId: '',
              inAccountId: '',
              amount: '',
              remark: ''
            }
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="handleAccountTransfer">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { accountList, accountTransferList, accountTransfer, queryAccountBalance } from '@/api/pingAnAccountTransfer'
// import moment from 'moment'

export default {
  data() {
    return {
      allOptions: [],
      outOptions: [],
      entryOptions: [],
      entryOptionsInDialog: [],
      account: '',
      modelForm: {
        businessIdType: 'ec_pop',
        outAccountId: '',
        inAccountId: '',
        amount: '',
        remark: ''
      },
      dialogVisible: false,
      formModel: {
        outAccountId: '',
        inAccountId: '',
        status: '',
        time: [],
        startTime: '',
        endTime: ''
      },
      tableColumns: [
        {
          prop: 'outAccountName',
          label: '出账账户'
        },
        {
          prop: 'inAccountName',
          label: '到账账户'
        },
        {
          prop: 'amount',
          label: '转账金额'
        },
        {
          prop: 'status',
          label: '状态'
        },
        {
          prop: 'createTime',
          label: '到账时间'
        },
        {
          prop: 'remark',
          label: '备注'
        }
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      formItem: [
        {
          label: '账户',
          prop: 'account',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '状态',
          prop: 'status',
          component: 'll-select',
          attrs: {
            options: [
              { desc: '全部', label: '全部', value: '' },
              { desc: '成功', label: '成功', value: 'finish' },
              { dese: '失败', label: '失败', value: 'create' }
            ]
          },
          width: '220px'
        }
      ],
      fullscreenLoading: false
    }
  },
  created() {
    this.getAccountType()
  },
  methods: {
    queryAccountBalance(val) {
      const item = this.allOptions.find(ite => ite.value === val)
      queryAccountBalance({
        accountId: item.value,
        accountType: item.accountType
      }).then(res => {
        this.account = +res.result
      })
    },
    handleChangeOutInDialog(val) {
      this.entryOptionsInDialog = []
      this.modelForm.inAccountId = ''
      if (`${val}` === '****************') {
        this.allOptions.forEach(item => {
          if (item.desc === '平台营销补贴户' || item.desc === '平台购物金户' || item.desc === '平台退佣金户' || item.desc === '平台退款垫资户') {
            this.entryOptionsInDialog.push(item)
          }
        })
      } else {
        this.allOptions.forEach(item => {
          if (item.desc === '平台提现专用户') {
            this.entryOptionsInDialog.push(item)
          }
        })
      }
      this.queryAccountBalance(val)
    },
    handleChangeOut(val) {
      this.entryOptions = []
      this.formModel.inAccountId = ''
      if (`${val}` === '****************') {
        this.allOptions.forEach(item => {
          if (item.desc === '平台营销补贴户' || item.desc === '平台购物金户' || item.desc === '平台退佣金户' || item.desc === '平台退款垫资户') {
            this.entryOptions.push(item)
          }
        })
      } else {
        this.allOptions.forEach(item => {
          if (item.desc === '平台提现专用户') {
            this.entryOptions.push(item)
          }
        })
      }
    },
    handleAccountTransfer() {
      if (this.modelForm.outAccountId === '') {
        this.$message.warning('请选择出账账户')
        return false
      }
      if (this.modelForm.inAccountId === '') {
        this.$message.warning('请选择到账账户')
        return false
      }
      if (this.modelForm.amount === '') {
        this.$message.warning('请输入转账金额')
        return false
      }
      if (this.modelForm.amount > this.account) {
        this.$message.warning('出账账户余额不足')
        return false
      }
      if (this.modelForm.remark === '') {
        this.$message.warning('请输入备注')
        return false
      }
      const params = { ...this.modelForm }
      params.amount = +params.amount * 100
      accountTransfer(params).then(res => {
        if (res.code === '1000') {
          this.$message.success(res.msg)
          this.dialogVisible = false
          this.handleGetList()
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getAccountType() {
      accountList().then(res => {
        if (res.result) {
          res.result.map(item => {
            item.desc = item.key
            item.label = item.key
          })
          this.allOptions = res.result
          this.allOptions.forEach(item => {
            if (item.desc !== '平台提现专用户') {
              this.outOptions.push(item)
            }
          })
        }
      })
    },
    handleGetList() {
      this.$refs['dataGrid'].loadData()
    },
    fetchData() {
      return new Promise(resolve => {
        accountTransferList(this.formModel)
          .then(response => {
            let { result } = response
            let tableData = {
              list: result,
              pagination: {}
            }
            console.log('4444')
            console.log(tableData)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: {}
            })
          })
      })
    },
    handleResetForm() {
      this.$refs.searchForm.resetFields()
      this.formModel = {
        outAccountId: '',
        inAccountId: '',
        status: '',
        time: [],
        startTime: '',
        endTime: ''
      }
      this.handleGetList()
    },
    handleInitiateTransfer() {
      this.dialogVisible = true
    },
    validateFormRequiredItem(val) {
      if (val) {
        this.formModel.startTime = new Date(val[0])
        this.formModel.endTime = new Date(val[1])
      } else {
        this.formModel.startTime = ''
        this.formModel.endTime = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
</style>
