{"name": "xyy-saas-payment-manage-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "dev:mock": "vue-cli-service serve --mode mock", "dev:test": "vue-cli-service serve --mode test", "dev:stage": "vue-cli-service serve --mode stage", "dev:staging": "vue-cli-service serve --mode staging", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode stage", "build:test": "vue-cli-service build --mode test", "build:dev": "vue-cli-service build --mode dev", "build:staging": "vue-cli-service build --mode staging", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"@babel/register": "^7.9.0", "@xyy-saas/async-validator": "1.0.1", "@xyy-saas/eslint-config-vue": "^0.1.2", "@xyy-saas/ui": "0.2.5", "@xyy-saas/validator": "^1.0.7", "axios": "^0.19.2", "core-js": "^3.6.4", "js-cookie": "^2.2.1", "mockjs": "^1.1.0", "moment": "^2.25.3", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^2.4.0", "qs": "^6.11.2", "register-service-worker": "^1.7.1", "svg-sprite-loader": "^5.0.0", "vue": "^2.6.11", "vue-router": "^3.1.6", "vuex": "^3.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-eslint": "~4.3.0", "@vue/cli-plugin-pwa": "~4.3.0", "@vue/cli-plugin-router": "~4.3.0", "@vue/cli-plugin-unit-jest": "~4.3.0", "@vue/cli-plugin-vuex": "~4.3.0", "@vue/cli-service": "~4.3.0", "@vue/test-utils": "1.0.0-beta.31", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.14.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}