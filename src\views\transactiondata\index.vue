<template>
  <div class="finance-stagement mt10">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form ref="searchForm" slot="search-form" class="statement-search-form" label-width="120px" :inline="true" :model="formModel">
        <!-- :rules="formRules" -->
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="商户编码" label-width="90px" prop="receiveId">
              <el-input v-model.trim="formModel.receiveId" placeholder="请输入商户编码" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商户名称" label-width="90px" prop="receiveName">
              <el-input v-model.trim="formModel.receiveName" placeholder="请输入商户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称" label-width="90px" prop="userName">
              <el-input v-model.trim="formModel.userName" placeholder="请输入客户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="药帮忙订单号" label-width="90px" prop="ybmOrderNo">
              <el-input v-model.trim="formModel.ybmOrderNo" placeholder="请输入药帮忙订单号" class="samewidth" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="支付单号" label-width="90px" prop="orderNo">
              <el-input v-model.trim="formModel.orderNo" placeholder="请输入支付单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <!-- 订单类型：默认全部，可筛选支付单、退款单 -->
          <el-col :span="6">
            <el-form-item label="订单类型" label-width="90px" prop="orderType">
              <el-select v-model="formModel.orderType" placeholder="请选择订单类型">
                <el-option label="全部" value="" />
                <el-option label="支付单" value="支付单" />
                <el-option label="退款单" value="退款单" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="清算状态" label-width="90px" prop="settleStatus">
              <el-select v-model="formModel.settleStatus" placeholder="请选择清算状态">
                <el-option label="全部" value="" />
                <el-option label="已清算" value="已清算" />
                <el-option label="未清算" value="未清算" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="处理时间" label-width="90px">
              <el-date-picker
                v-model="formModel.dealTime"
                type="daterange"
                format="yyyy-MM-dd"
                :range-separator="'至'"
                :clearable="true"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="validateFormRequiredItem"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row> 
            <el-col :span="6">
            <el-form-item label="商户号" label-width="90px" prop="merchantNo">
              <el-select v-model="formModel.merchantNo" placeholder="请选择商户号">
                <el-option label="快捷支付_133400097004" value="133400097004" />
                <el-option label="快捷支付_133400097005" value="133400097005" />
                <el-option label="聚合支付_135647451008" value="135647451008" />
                <el-option label="聚合支付_133400097006" value="133400097006" />
                <el-option label="聚合支付支付宝_133400097002" value="133400097002" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付子单号" label-width="90px" prop="subPayNo">
              <el-input v-model.trim="formModel.subPayNo" placeholder="请输入支付子单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="checkboxCol">
            <el-checkbox :true-label="1" :false-label="0" v-model="formModel.parentIsPay">是否母单付款</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div>
              <ll-button @click="handleExportFile">导出Excel</ll-button>
              <ll-button @click="handleResetForm">重置</ll-button>
              <ll-button type="primary" @click="handleGetList">查询</ll-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
      >
        <!-- <template #payDay="{record}">
          <span>{{ (record.payDay) ? `${moment(record.payDay).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #curRepayDate="{record}">
          <span>{{ (record.curRepayDate) ? `${moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #actRepayDate="{record}">
          <span>{{ (record.actRepayDate) ? `${moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #payoffflag="{record}">
          <span>{{ {1:'已结清',0:'未结清'}[record.payoffflag] }}</span>
        </template> -->
      </ll-data-grid>
    </ll-list-page-layout>
  </div>
</template>

<script>
import { getList, exportData } from '@/api/pinganLoan.js'
// 引入处理时间戳
import moment from 'moment'
import { download } from '@/utils/index.js'

export default {
  data() {
    return {
      payDay: [],
      formModel: {
        receiveId: '',
        receiveName: '',
        userName: '',
        ybmOrderNo: '',
        orderNo: '',
        orderType: '',
        settleStatus: '',
        dealTime: [],
        merchantNo: '133400097004',
        dealTimeStart: '',
        dealTimeEnd: '',
        subPayNo: '',
        parentIsPay: 0,
      },
      tableColumns: [
        {
          prop: 'merchantNo',
          label: '商户号',
          'min-width': '120',
          // formatter: record => (record.payDay ? moment(record.payDay).format('YYYY-MM-DD HH:mm:ss') : '--')
        },
        {
          prop: 'receiveId',
          label: '商户编码',
          'min-width': '120'
        },
        {
          prop: 'receiveName',
          label: '商户名称',
          'min-width': '120'
        },
        {
          prop: 'userName',
          label: '客户名称',
          'min-width': '120'
        },
        {
          prop: 'ybmOrderNo',
          label: '药帮忙订单号',
          'min-width': '120',
          // formatter: record => (record.payDay ? moment(record.payDay).format('YYYY-MM-DD HH:mm:ss') : '--')
        },
        {
          prop: 'orderNo',
          label: '订单号',
          'min-width': '120'
        },
        {
          prop: 'bankOrderNo',
          label: '银行订单号',
          'min-width': '120'
        },
        {
          prop: 'parentIsPayStr',
          label: '是否母单付款',
          'min-width': '120'
        },
        {
          prop: 'subPayNo',
          label: '支付子单号',
          'min-width': '120'
        },
        {
          prop: 'subPayAmount',
          label: '子单金额',
          'min-width': '95'
        },
        {
          prop: 'orderAmount',
          label: '订单金额',
          'min-width': '95'
        },
        {
          prop: 'businessType',
          label: '业务类型',
          'min-width': '95',
          // formatter: record => (record.curRepayDate ? moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss') : '--')
        },
        {
          prop: 'orderType',
          label: '订单类型',
          'min-width': '95',
          // formatter: record => (record.actRepayDate ? moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss') : '--')
        },
        {
          prop: 'orderStatus',
          label: '订单状态',
          'min-width': '95'
        },
        {
          prop: 'refundAmount',
          label: '已退款金额',
          'min-width': '95'
        },
        {
          prop: 'settleStatus',
          label: '清算状态',
          'min-width': '95'
        },
        {
          prop: 'tradeTime',
          label: '交易时间',
          'min-width': '95'
        },
        {
          prop: 'dealTime',
          label: '处理时间',
          'min-width': '95'
        },
        {
          prop: 'oriOrderNo',
          label: '原订单号',
          'min-width': '95'
        },
        {
          prop: 'fee',
          label: '手续费',
          'min-width': '95'
        },
        {
          prop: 'accountName',
          label: '账户名称',
          'min-width': '95'
        },
        {
          prop: 'bankName',
          label: '银行名称',
          'min-width': '95'
        },
        {
          prop: 'businessOrderNo',
          label: '业务订单号',
          'min-width': '95'
        },
        {
          prop: 'tradeNo',
          label: '交易号',
          'min-width': '95'
        },
        {
          prop: 'platMerchantNo',
          label: '平台商户订单号',
          'min-width': '95'
        },
        {
          prop: 'platMerchant',
          label: '平台商户号',
          'min-width': '95'
        },
        {
          prop: 'platMerchantName',
          label: '平台商户名称',
          'min-width': '95'
        },
        {
          prop: 'platInTransitMerchant',
          label: '平台在途户',
          'min-width': '95'
        },
        {
          prop: 'subMerchant',
          label: '子商户号',
          'min-width': '95'
        },
        {
          prop: 'commissionMsg',
          label: '佣金信息',
          'min-width': '95'
        },
        {
          prop: 'marketingMsg',
          label: '营销信息',
          'min-width': '95'
        },
        {
          prop: 'commissionAmount',
          label: '佣金总金额',
          'min-width': '95'
        },
        {
          prop: 'remark',
          label: '备注',
          'min-width': '95',
          'show-overflow-tooltip': true
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false
    }
  },
  created() {
    this.payDay = [this.lastMonthFirstDay(), moment().subtract(1, 'days').format('YYYY-MM-DD')]
    this.formModel.dealTime = this.payDay
    this.formModel.dealTimeStart = this.payDay[0]
    this.formModel.dealTimeEnd = this.payDay[1]
  },
  mounted() {},
  methods: {
    lastMonthFirstDay() {
      return moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD')
    },
    // 订单支付时间
    validateFormRequiredItem(val) {
      if (val) {
        this.formModel.dealTimeStart = moment(val[0]).format('YYYY-MM-DD')
        this.formModel.dealTimeEnd = moment(val[1]).format('YYYY-MM-DD')
      } else {
        this.formModel.dealTimeStart = ''
        this.formModel.dealTimeEnd = ''
      }
    },
    handleGetList() {
      this.$refs['dataGrid'].loadData()
    },
    fetchData({pagination}) {
      console.log(pagination)
      const { pageSize, currentPage } = pagination
      const params = Object.assign({ pageSize: pageSize, page: currentPage }, this.formModel)
      return new Promise(resolve => {
        getList(params)
          .then(response => {
            let { data, totalCount, pageSize } = response
            console.log('data', data)
            let tableData = {
              list: data,
              pagination: {
                pageSize: pageSize,
                total: totalCount
              }
            }
            // let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },
    handleResetForm() {
      this.$refs.searchForm.resetFields()
      this.payDay = [
        this.lastMonthFirstDay(),
        moment()
          .subtract(1, 'days')
          .format('YYYY-MM-DD')
      ]
      this.formModel.dealTime = this.payDay
      this.formModel.dealTimeStart = this.payDay[0]
      this.formModel.dealTimeEnd = this.payDay[1]
      this.pagination = {
        currentPage: 1,
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      }
      this.handleGetList()
    },
    // 导出excel
    handleExportFile() {
      if (moment(this.formModel.dealTimeEnd).diff(moment(this.formModel.dealTimeStart), 'days') > 31) {
        this.$message.warning('只允许导出处理时间区间为31天的数据')
        return false
      }
      const params = JSON.parse(JSON.stringify(this.formModel))
      delete params.dealTime
      this.fullscreenLoading = true
      exportData(params)
        .then(res => {
          const result = res.data
          const fileExportName =
            res && res.headers && res.headers['export-filename'] ? decodeURIComponent(res.headers['export-filename']) : '京东交易数据.csv'
          download(result, fileExportName)
        })
        .finally(() => {
          this.fullscreenLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
.checkboxCol {
  padding-left: 30px !important;
  padding-top: 10px;
}
::v-deep .checkboxCol .el-checkbox__label {
  font-weight: 600;
}
</style>