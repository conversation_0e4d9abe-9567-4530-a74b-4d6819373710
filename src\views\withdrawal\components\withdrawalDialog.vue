<!-- 适用门店 -->
<template>
  <el-dialog :visible.sync="withdrawalDialog" width="800px" title="提现" center destroy-on-close @close="close">
    <hr />
    <div class="maintable">
      <div class="tableitem">
        <p class="ptitle">提现账户</p>
        <p>账户名称：{{ row.accountName }}</p>
        <p>账号：{{ row.accountId }}</p>
        <p>可提现余额：{{ row.residual }}元</p>
      </div>
      <div class="dash">{{ empty }}</div>
      <div class="tableitem">
        <p class="ptitle">到账账户</p>
        <p>账户名称：{{ row.settleName }}</p>
        <p>账号：{{ row.settleAccount }}</p>
      </div>
    </div>
    <hr />
    <div class="formbox">
      <el-form class="target-user-box" :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item class="fitem" label="提现金额：" prop="WithdrawalAmount">
          <el-input class="samewidth" v-model="ruleForm.WithdrawalAmount" @input="changeA" placeholder="请输入提现金额" />
          <span class="ptitle marginleft">元</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="txtbox">
      <div class="marginb">
        <span>提现手续费收费标准</span>
      </div>
      <div class="marginb disflex">
        <div class="w80 disflex">1.富民银行</div> 
        <div>对私/公帐户：0元/笔</div>
      </div>
      <div class="disflex">
        <div class="w80 otherBankLeft">
          2.他行
        </div>
        <div class="otherBank">
          <div class="text-flex">
            <div class="leftflex">
              <div class="txtitem"> 对私账户：</div>
              <div class="txtitem"> 对公账户：</div>
            </div>
            <div class="text-right">
              <div class="txtitem"> 0.2元/笔 </div>
              <div class="txtitem"> <span class="w120">0-5万元（含）</span>0.5元/笔</div>
              <div class="txtitem"> <span class="w120">5-20万元（不含）</span> 5元/笔 （7*24）</div>
              <div class="txtitem"> <span class="w120">20万元以上（含）</span> 6元/笔 （工作日9：00-17：00）</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span v-if="!disabled" slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :loading="isAwait" type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { querywithdrawalSubmit } from '@/api/report'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    // 表单验证
    var checkAmount = (rule, value, callback) => {
      if(value === null || value === '') {
        return callback(new Error('提现金额不能为空'))
      }else {
        let val = Number(value)
        if(val === 0) {
          return callback(new Error('0元无法提现'))
        }
        if(val<0) {
          return callback(new Error('请输入正确的提现金额'))
        }
        if(val > Number(this.row.residual)) {
          return callback(new Error('可提现余额不足'))
        }
        if(value.includes('.') && value.split('.')[1].length>2){
          return callback(new Error('提现金额最多支持到小数点后两位'))
        }
        callback()
      }
    }
    return {
      ruleForm: {
        WithdrawalAmount: null //提现金额
      },
      empty: '',
      loading: false,
      isAwait: false,
      withdrawalDialog: true,
      rules: {
        WithdrawalAmount: [{ validator: checkAmount, trigger: 'blur' }]
      }
    }
  },
  methods: {
    changeA(val) {
        val = val.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符  
        val= val.replace(/^\./g,"");  //验证第一个字符是数字而不是. 
        val= val.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.   
        this.ruleForm.WithdrawalAmount = val.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    },
    close() {
      this.$emit('update:visible', false)
    },
    // 点击提交
    handleSubmit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.submit()
        } else {
          return false
        }
      })
    },
    //提交请求
    async submit() {
      let params = {
        accountId: this.row.accountId,
        amount: this.ruleForm.WithdrawalAmount
      }
      this.isAwait = true
      try {
        await querywithdrawalSubmit(params)
        this.isAwait = false
        this.close()
        await this.$parent.refreshList()
        this.$notify({
            title: '申请提现成功',
            message: '预计1～3个工作日内到账，实际到账时间以银行侧为准',
            type: 'success',
            duration: 3000
        });
      } catch (error) {
        this.close()
        this.isAwait = false
        console.log(error)
      }
     
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 20px;
  height: 536px;
}
.el-dialog__footer {
  .dialog-footer {
    display: block;
    min-height: 34px;
  }
}
.maintable {
  display: flex;
}
.dash {
  width: 1px;
  height: 150px;
  border: 1px dashed #666666;
}
.ptitle {
  margin-left: 0;
  text-align: center;
}
.tableitem {
  width: 49%;
  p {
    margin-left: 30px;
  }
}
p {
  font-size: 16px;
}
.samewidth {
  width: 220px;
}
.target-user-box {
  width: 100%;
}
.formbox {
  display: flex;
  margin-bottom: 15px;
}
.fitem {
  display: flex;
  justify-content: center;
}
.marginleft {
  margin-left: 10px;
}
.txtbox {
  padding-left: 150px;
  display: flex;
  flex-direction: column;
}
.marginb {
  margin-bottom: 15px;
}
.disflex{
  display: flex;
}
.otherBank{
  display:flex;
  flex: 1;
  flex-direction:column;
}
.w80{
  width: 80px;
  display: inline-block;
}
.w120{
  display: inline-block;
  width: 120px;
}
.otherBankLeft{
}
.leftflex{
  flex: 0 0 90px;
}
.text-flex{
  display: flex;
  .text-right{
    
  }
}
.txtitem{
  padding-bottom: 4px;
}
</style>
