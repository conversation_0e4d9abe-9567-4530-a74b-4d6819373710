import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import './styles/element-variables.scss'
import 'element-ui/lib/theme-chalk/index.css'
import './styles/font/font.css'
import asyncValidator from '@xyy-saas/async-validator'

import '@/styles/index.scss' // global css
import '@/styles/public.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // permission control
import * as filters from './filters' // global filters
import './vuePrototype' // global prototype

// /**
//  * If you don't want to use mock-server
//  * you want to use MockJs for mock api
//  * you can execute: mockXHR()
//  *
//  * Currently MockJs will be used in the production environment,
//  * please remove it before going online ! ! !
//  */
// console.log('process.env.NODE_ENV: ', process.env.NODE_ENV);

// if (process.env.NODE_ENV === "production") {
//   const { mockXHR } = require("../mock");
//   mockXHR();
// }

// document.cookie = 'sid=96f2144f-d49f-4a5b-919d-244c2792fbff'
// document.cookie = 'uid=CgoUFF+NdLUgGDBWA1qqAg=='
// document.cookie = 'zg_did=%7B%22did%22%3A%20%22175448202588c7-0c3b3834c5e757-333769-144000-17544820259471%22%7D'
// document.cookie =
//   'TGC=eyJhbGciOiJIUzUxMiJ9.ZXlKNmFYQWlPaUpFUlVZaUxDSmhiR2NpT2lKa2FYSWlMQ0psYm1NaU9pSkJNVEk0UTBKRExVaFRNalUySW4wLi5BSDhFUDJrSkt4YldCUUxhcXppMXV3LmFzZ3JxOFlQZ0VWeW1ZQlVpaUhsRWhDVjRTaEo5S1o2aV9yZEFXQzNOZElUdUd4anZLelFDUmpjM0pQNW5ReFdmSGxGM1JBMzRqcjg4TnRVY3ZxcGp3ckhuUW1FNWtPa0tTMlhMQ2MzdVQ5WlJ2WHFwdlpPYWFEb2R1blppQmRfMnBiUXZuZm8zNWJ5bFpPQlJha0lsR3hKV3JZcDFfellWOVk3UDVwLXJUSXQxV0FvY2hqUjYtQXZoVTYwVHR6VmR4eUYzM0JTOHBZeHA1LVlsRW9uN2RhOW9Sbi1QX0xOZ25TT21LNmJBTFhjZGpNQTNKSXpKTzMtNHdodGs0dExpTUsweDNPRXd1dTFXNWtWTWhjWlVBLmdmQXRsSHd4REpTclBSOEliZ1BrMFE=.eBjIEBlFHKPLN3WlI5nFook3qgmoOOvWv3Mer5BviGy4W_q1K4d4s9IHlr3QsZKH2iG0TbJJIeB29dZlBt9mhA'
// document.cookie = 'token=token'
// localStorage.setItem(
//   'userInfo',
//   JSON.stringify({
//     SET_NAME: 'renhui',
//     SET_AVATAR: '',
//     SET_DEPARTMENTNAME: '',
//     SET_INTRODUCTION: ''
//   })
// )
ElementUI.Form.props.validateCore.default = asyncValidator

Vue.use(ElementUI)
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
