import request from '@/utils/request'
import requestDownload from '@/utils/requestDownload'

/**
 * 支付后台_京东账单查询_0028_交易数据
 */
export function getList(data) {
  return request({
    url: '/jd/page_0028',
    method: 'post',
    data
  })
}

/**
 * 支付后台_京东账单导出_0028_交易数据
 */
export function exportData(data) {
  return requestDownload({
    url: '/jd/export_0028',
    method: 'post',
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data
  })
}

/**
 * 支付后台_京东账单查询_0026_清算数据
 */
export function getSettledata(data) {
  return request({
    url: '/jd/page_0026',
    method: 'post',
    data
  })
}

/**
 * 支付后台_京东账单导出_0026_清算数据
 */
export function exportSettledata(data) {
  return requestDownload({
    url: '/jd/export_0026',
    method: 'post',
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data
  })
}

/**
 * 支付后台_京东账单查询_0027_支付手续费
 */
export function getFeedata(data) {
  return request({
    url: '/jd/page_0027',
    method: 'post',
    data
  })
}

/**
 * 支付后台_京东账单导出_0027_支付手续费
 */
export function exportFeedata(data) {
  return requestDownload({
    url: '/jd/export_0027',
    method: 'post',
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data
  })
}
