<template>
    <div class="finance-stagement mt10">
      <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
        <el-form
          ref="searchForm"
          slot="search-form"
          class="statement-search-form"
          label-width="120px"
          :inline="true"
          :model="formModel"
          >
          <!-- :rules="formRules" -->
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="商户名称" label-width="90px" prop="receiverName">
              <el-input v-model.trim="formModel.receiverName" placeholder="请输入商户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称" label-width="90px" prop="payerName">
              <el-input v-model.trim="formModel.payerName" placeholder="请输入客户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="平台订单号" label-width="90px" prop="businessPayNo">
              <el-input v-model.trim="formModel.businessPayNo" placeholder="请输入平台订单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单支付时间" label-width="100px" prop="payDay">
              <el-date-picker
                v-model="payDay"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="timestamp"
                :range-separator="'至'"
                :clearable="true"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="validateFormRequiredItem"
                >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="借据号" label-width="90px" prop="loanBalanceNo">
              <el-input v-model.trim="formModel.loanBalanceNo" placeholder="请输入借据号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="还款状态" label-width="90px" prop="payoffflag">
              <el-select v-model="formModel.payoffflag" placeholder="请选择还款状态">
                <el-option label="已结清" :value="1" />
                <el-option label="未结清" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="应还款日" label-width="90px">
              <el-date-picker
                v-model="curRepayDate"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="timestamp"
                :clearable="true"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handlePickerDueDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实际还款日" label-width="90px">
              <el-date-picker
                v-model="actRepayDate"
                format="yyyy-MM-dd"
                value-format="timestamp"
                type="daterange"
                :clearable="true"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handlePickerPractical"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div>
              <ll-button @click="handleExportFile">导出Excel</ll-button>
              <ll-button @click="handleResetForm">重置</ll-button>
              <ll-button type="primary" @click="handleGetList">查询</ll-button>
            </div>
          </el-col>
        </el-row>
        </el-form>
        <ll-data-grid
          ref="dataGrid"
          :pagination-page-size.sync="pagination.pageSize"
          :pagination-page-sizes="pagination.sizes"
          :table-columns="tableColumns"
          :async-fetch-data-fun="fetchData"
        >
        <template #payDay="{record}">
          <span>{{ (record.payDay) ? `${moment(record.payDay).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #curRepayDate="{record}">
          <span>{{ (record.curRepayDate) ? `${moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #actRepayDate="{record}">
          <span>{{ (record.actRepayDate) ? `${moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #payoffflag="{record}">
          <span>{{ {1:'已结清',0:'未结清'}[record.payoffflag] }}</span>
        </template>
        </ll-data-grid>
      </ll-list-page-layout>
    </div>
  </template>
  
  <script>
  import {getProjectList,exportProject} from '@/api/openAccount.js'
  import { parseTime, download } from '@/utils/index.js'
  import qs from 'qs';
  // 引入处理时间戳
  import moment from 'moment'
  export default {
    data() {
      return {
        qs,
        payDay:[],
        actRepayDate:[], //实际还款日
        curRepayDate:[], //应还款日
        formModel: {
            receiverName: '', // 商户名称
            payerName: '', //客户名称
            // 支付日期
            startPayDate:'',
            endPayDate:'',
            // 应还日
            startCurRepayDate:'',
            endCurRepayDate:'',
            // 实还日
            startActRepayDate:'',
            endActRepayDate:'',
            businessPayNo:'', //平台订单号
            loanBalanceNo:'', //借据号
            payoffflag:'' //还款状态
        },
        tableColumns: [
          {
            prop: 'receiverName',
            label: '商户名称',
            'min-width': '120'
          },
          {
            prop: 'payerName',
            label: '客户名称',
            'min-width': '200'
          },
          {
            prop: 'businessPayNo',
            label: '平台订单号',
            'min-width': '200'
          },
          {
            prop: 'payDay',
            label: '订单支付时间',
            'min-width': '200',
            formatter:(record) =>
            record.payDay ? moment(record.payDay).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'loanBalanceNo',
            label: '借据号',
            'min-width': '200'
          },
          {
            prop: 'curTerm',
            label: '期次',
            'min-width': '95'
          },
          {
            prop: 'curNum',
            label: '提前还款次数',
            'min-width': '95'
          },
          {
            prop: 'curRepayDate',
            label: '应还款日',
            'min-width': '95',
            formatter:(record) =>
            record.curRepayDate ? moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'actRepayDate',
            label: '实际还款日',
            'min-width': '95',
            formatter:(record) =>
            record.actRepayDate ? moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'payoffflag',
            label: '还款状态标记',
            'min-width': '95',
            formatter:(record) =>{
              return {1:'已结清',0:'未结清'}[record.payoffflag]
            }
          },
          {
            prop: 'curRepayAmount',
            label: '应还总金额',
            'min-width': '95'
          },
          {
            prop: 'actRepayAmount',
            label: '实还总金额',
            'min-width': '95'
          },
          {
            prop: 'curPrincipalAmount',
            label: '应还本金',
            'min-width': '95'
          },
          {
            prop: 'actPrincipalAmount',
            label: '实还本金',
            'min-width': '95'
          },
          {
            prop: 'curInterestAmount',
            label: '应还利息',
            'min-width': '95'
          },
          {
            prop: 'curSubsidyInterestAmount',
            label: '应还利息平台贴息',
            'min-width': '95'
          },
          {
            prop: 'actInterestAmount',
            label: '实还利息',
            'min-width': '95'
          },
          {
            prop: 'actSubsidyInterestAmount',
            label: '实还利息平台贴息',
            'min-width': '95'
          },
          {
            prop: 'curCompoundInterestAmount',
            label: '应还复利',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '实还复利',
            'min-width': '95'
          },
        ],
        pagination: {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        fullscreenLoading: false,
      }
    },
    created(){
      this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
      this.formModel.startPayDate = this.payDay[0]
      this.formModel.endPayDate = this.payDay[1]
    },
    computed: {
    },
    mounted() {},
    methods: {
      lastMonthFirstDay(){
        const date = new Date()
        date.setMonth(date.getMonth() -1);//获取上一个月
        date.setDate(1);//设置为1号
        return date.getTime();
      },
      // 实际还款日
      handlePickerPractical(val){
        if (val) {
          this.formModel.startActRepayDate = val[0]
          this.formModel.endActRepayDate = val[1]
        } else {
          this.formModel.startActRepayDate = ''
          this.formModel.endActRepayDate = ''
        }
      },
      // 应还款日
      handlePickerDueDate(val){
        console.log(val);
        if (val) {
          this.formModel.startCurRepayDate = val[0]
          this.formModel.endCurRepayDate = val[1]
        } else {
          this.formModel.startCurRepayDate = ''
          this.formModel.endCurRepayDate = ''
        }
      },
      // 订单支付时间
      validateFormRequiredItem(val) {
        if (val) {
          this.formModel.startPayDate = val[0]
          this.formModel.endPayDate = val[1]
        } else {
          this.formModel.startPayDate = ''
          this.formModel.endPayDate = ''
        }
      },
      handleGetList() {
        this.$refs['dataGrid'].loadData()
      },
      fetchData({pagination}) {
        console.log(pagination);
        const { pageSize, currentPage } = pagination;
        const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
        return new Promise(resolve => {
          getProjectList(params)
            .then(response => {
              let { data,totalCount,pageSize } = response
              console.log('data',data);
              let tableData = {
                list: data,
                pagination: {
                  pageSize: pageSize,
                  total: totalCount
                }
              }
              // let tableData = this.formatterTableData(data, pagination, totalCount)
              resolve(tableData)
            })
            .catch(() => {
              resolve({
                list: [],
                pagination: { pageSize: 10, total: 0 }
              })
            })
        })
      },
      formatterTableData(tableData, pagination, totalCount) {
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
      handleResetForm() {
        this.$refs.searchForm.resetFields()
        this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
        this.actRepayDate = [], //实际还款日
        this.curRepayDate = [], //应还款日
        this.formModel = {
            receiverName: '', // 商户名称
            payerName: '', //客户名称
            // 支付日期
            startPayDate:this.payDay[0],
            endPayDate:this.payDay[1],
            // 应还日
            startCurRepayDate:'',
            endCurRepayDate:'',
            // 实还日
            startActRepayDate:'',
            endActRepayDate:'',
            businessPayNo:'', //平台订单号
            loanBalanceNo:'', //借据号
            payoffflag:'' //还款状态
        },
        this.pagination = {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        this.handleGetList()
      },
        // 导出excel
        handleExportFile() {
          // console.log(this.qs);
          // const params = decodeURIComponent(qs.stringify(this.formModel));
          // location.href = `/api/pinganLoan/repaymentPlan/export?${params}`
          const exportName = '还款计划'
          exportProject({...this.formModel})
            .then(response => {
              console.log(response);
              let result = response.data
              const dateStr = parseTime(new Date(), '{y}{m}{d}')
              const defExportName = `${exportName}-${dateStr}.xlsx`
              const fileExportName =
                response && response.headers && response.headers['export-filename']
                  ? decodeURIComponent(response.headers['export-filename'])
                  : defExportName
              download(result, fileExportName)
            })
            .finally(() => {
              // this.fullscreenLoading = false
            })
        },
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
  </style>
  