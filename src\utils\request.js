import axios from "axios";
// import Qs from "qs";
import { MessageBox, Message } from "element-ui";
import store from "@/store";
import router from "@/router";
// create an axios instance

console.log(process.env.VUE_APP_BASE_API);
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // baseURL: 'https://saascrm-web.dev.ybm100.com/saascrm/api', // url = base url + request url
  // baseURL: 'https://saascrm-web.test.ybm100.com/saascrm/api', // url = base url + request url
  withCredentials: true // send cookies when cross-domain requests sso需要此参数
  // timeout: 5000 // request timeout
});
/**
 * HTTP方法
 */
// axios.defaults.headers.common["Content-Type"] =
//   "application/json;charset=UTF-8";
// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      // let each request carry token --['X-Token'] as a custom key.
      // please modify it according to the actual situation.
      config.headers["token"] = store.getters.token;
    }
    config.headers["locationUrl"] = window.location.href;
    // sso需要此参数
    config.headers["X-Requested-With"] = "XMLHttpRequest";
    // config.headers["Content-Type"] = "application/json;charset=utf-8";
    if (config.method == "post") {
      // config.data = Qs.stringify(config.data);
      // console.log(config);
    }
    return config;
  },
  error => {
    // do something with request error
    // console.log("error: ", error); // for debug
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code.
   */
  response => {
    // debugger
    const res = response.data;
    console.log("response: ", response);
    if (response.config.responseType === "blob" ) {
      return response;
    }

    // if the custom code is not 1000, it is judged as an error.
    // console.log('res.code.......', res)
    if (response.config.url == "/logout/default") {
      return {
        code: "1000",
        displayMsg: "",
        msg: "success",
        result: {}
      };
    } else if (res.code !== "1000" && res.code !== 0) {
      const errorMsg = res.displayMsg || res.msg || "error";
      if (res.code === 401) {
        window.location.href = res.url;
      } else if (res.code === 9008 || res.code === 9009) {
        // 9008：登录过期 9009：被挤掉线
        // to re-login
        MessageBox.confirm(errorMsg, "确认注销", {
          confirmButtonText: "重新登录",
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          //   showCancelButton: false,
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          store.dispatch("user/resetToken").then(() => {
            location.reload();
          });
        });
      } else if (res.code === 403) {
        // to re-login
        router.push({
          path: "/401"
        });
      } else if (res.code === 404) {
        // to re-login
        router.push({
          path: "/404"
        });
      } else if (res.code == '9011') {
        // 账号被停用提示信息
        MessageBox.confirm(errorMsg, "提示", {
          confirmButtonText: "确认",
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          showCancelButton: false,
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          store.dispatch("user/logout").then(() => {
            location.reload();
          });
        });
      } else {
        Message({
          message: errorMsg,
          type: "error",
          duration: 5 * 1000
        });
      }
      return Promise.reject(errorMsg);
    } else {
      return res;
    }
  },
  error => {
    console.log("error" + error); // for debug
    if (error && error.response) {
      let response = error.response || {};
      let errorMsg = "请求失败，请稍后重试";
      switch (response.status) {
        case 520:
          errorMsg = "参数错误";
          break;
        case 521:
          errorMsg = "单次最多导出10w条数据";
          break;
        case 522:
          errorMsg = "系统繁忙";
          break;
        default:
          break;
      }

      Message({
        message: errorMsg,
        type: "error",
        duration: 5 * 1000
      });
    } else {
      Message({
        message: error.message,
        type: "error",
        duration: 5 * 1000
      });
    }
    return Promise.reject(error);
  }
);

export default service;
