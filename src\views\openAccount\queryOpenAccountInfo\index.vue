<template>
  <div>
    <ll-list-page-layout >
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="平台侧用户ID" label-width="100px" prop="accountId">
          <el-input v-model="formModel.accountId" placeholder="平台用户ID" />
        </el-form-item>

        <el-form-item label="业务侧用户ID" label-width="100px" prop="businessId">
          <el-input v-model="formModel.businessId" placeholder="业务侧用户ID" />
        </el-form-item>

        <el-form-item label="业务类型" label-width="90px" prop="businessIdType">
          <ll-select v-model="formModel.businessIdType" :options="formItem[1].attrs['options']" />
        </el-form-item>

        <el-form-item label="业务产品线" label-width="90px" prop="channelProduct">
          <ll-select v-model="formModel.channelProduct" :options="formItem[0].attrs['options']" />
        </el-form-item>

        <el-form-item label=" " label-width="90px">
          <ll-button type="primary" @click="handleFormSubmit">查询</ll-button>
        </el-form-item>
      </el-form>

      <div style="display: flex; flex-wrap: wrap;" v-if="openAccountInfo != undefined">
        <el-card v-loading="loading" class="box-card" v-if="openAccountInfo.userInfo != undefined">
          <div slot="header" class="clearfix">
            <span>用户开户基本信息（user_info）</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="openUpdateUserInfoDialog">修改</el-button>
          </div>

          <div class="text item">公司名称 : {{ openAccountInfo.userInfo.name }}</div>
          <div class="text item">电话号码 : {{ openAccountInfo.userInfo.mobile }}</div>
          <div class="text item">办公地址 : {{ openAccountInfo.userInfo.address }}</div>
          <div class="text item">调用方的业务类型 : {{ openAccountInfo.userInfo.businessIdType }}</div>
          <el-collapse>
            <el-collapse-item title="联系人信息（contactInfo）">
              <div :key="contactInfo.userId" v-for="contactInfo in openAccountInfo.userInfo.contactInfos">
                <div>商户的userId(businessId) : {{ contactInfo.userId }}</div>
                <div>联系人类型 : {{ contactInfo.contactType }}</div>
                <div>联系人名称 : {{ contactInfo.contactName }}</div>
                <div>证件类型 : {{ contactInfo.contactCertType }}</div>
                <div>联系人证件号 : {{ contactInfo.contactCertNo }}</div>
                <div>证件起始日期 : {{ contactInfo.contactCertValidFrom }}</div>
                <div>证件结束日期 : {{ contactInfo.contactCertValidUntil }}</div>
                <div>联系人手机号 : {{ contactInfo.contactCertMobile }}</div>
              </div>
            </el-collapse-item>
            <el-collapse-item title="身份信息（identityInfo）">
              <div>证件号码 : {{ openAccountInfo.userInfo.cardId }}</div>
              <div>证件类型(1.身份证 2.营业执照 3.统一信用代码) : {{ openAccountInfo.userInfo.identityType }}</div>
              <div>证件起始日期 : {{ openAccountInfo.userInfo.validFrom }}</div>
              <div>证件结束日期 : {{ openAccountInfo.userInfo.validUntil }}</div>
              <div>行业 : {{ openAccountInfo.userInfo.industry }}</div>
              <div>职业(个人注册必填) : {{ openAccountInfo.userInfo.profession }}</div>
            </el-collapse-item>
            <el-collapse-item title="结算信息（settleInfo）">
              <div>接收者银行账号/卡号 : {{ openAccountInfo.userInfo.account }}</div>
              <div>收款者银行开户名 : {{ openAccountInfo.userInfo.settleName }}</div>
              <div>银行卡预留手机号 : {{ openAccountInfo.userInfo.settleMobile }}</div>
              <div>银行名称 : {{ openAccountInfo.userInfo.openBank }}</div>
              <div>银行卡号类型 : {{ openAccountInfo.userInfo.cardType }}</div>
              <div>开户行名称 : {{ openAccountInfo.userInfo.subBank }}</div>
              <div>开户行行号 : {{ openAccountInfo.userInfo.subBankCode }}</div>
              <div>业务内部用户名称 : {{ openAccountInfo.userInfo.businessUserName }}</div>
              <div>结算用户创建成功通知地址 : {{ openAccountInfo.userInfo.callbackAddress }}</div>
            </el-collapse-item>
            <el-collapse-item title="图片信息（picMap）">
              <div style="display: flex; flex-wrap: wrap;">
                <div class="demo-image__preview" v-for="item in userInfoPicUrls" :key="item.key" style="margin: 5px;">
                  <el-image :title="item.key" style="width: 100px; height: 100px"
                            :src="item.url" :preview-src-list="userInfoPicUrlList"/>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <el-card v-loading="loading" class="box-card" v-if="openAccountInfo.createAllPayInfo != undefined">
          <div slot="header" class="clearfix">
            <span>用户报件基本信息（{{openAccountInfo.createAllPayInfo.status}}）</span>
            <el-popconfirm
              :hide-icon="false"
              :title="openAccountInfo.createAllPayInfo.message">
              <el-button slot="reference">状态说明</el-button>
            </el-popconfirm>
            <el-button type="primary" style="margin: 0 3px" @click="openPayOpen=true">支付功能</el-button>
            <el-button style="float: right; padding: 3px 0" type="text">修改</el-button>
          </div>

          <el-collapse>
            <el-collapse-item title="平台信息">
              <div>支付平台id : {{ openAccountInfo.createAllPayInfo.userId }}</div>
              <div>接口调用方的用户id : {{ openAccountInfo.createAllPayInfo.businessId }}</div>
              <div>接口调用方的业务类型 : {{ openAccountInfo.createAllPayInfo.businessIdType }}</div>
              <div>账户渠道选择 : {{ openAccountInfo.createAllPayInfo.accountChannel }}</div>
              <div>微信特约商户号 : {{ openAccountInfo.createAllPayInfo.wxSubMchid }}</div>
              <div>回调地址 : {{ openAccountInfo.createAllPayInfo.callback }}</div>
            </el-collapse-item>
            <el-collapse-item title="基本信息">
              <div>商户号 : {{ openAccountInfo.createAllPayInfo.merchantNo }}</div>
              <div>商户侧id : {{ openAccountInfo.createAllPayInfo.custMerchantNo }}</div>
              <div>商户登陆手机号 : {{ openAccountInfo.createAllPayInfo.mobile }}</div>
              <div>商户登陆邮箱 : {{ openAccountInfo.createAllPayInfo.email }}</div>
            </el-collapse-item>
            <el-collapse-item title="企业信息">
              <div>企业类型 : {{ openAccountInfo.createAllPayInfo.companyType }}</div>
              <div>企业简称 : {{ openAccountInfo.createAllPayInfo.companyShortName }}</div>
              <div>经营范围 : {{ openAccountInfo.createAllPayInfo.businessScope }}</div>
              <div>注册资本金 : {{ openAccountInfo.createAllPayInfo.registeredCapital }}</div>
              <div>注册资本金币种 : {{ openAccountInfo.createAllPayInfo.registeredCapitalCurrency }}</div>
              <div>注册地址 : {{ openAccountInfo.createAllPayInfo.registerAddress }}</div>
              <div>注册省份代码 : {{ openAccountInfo.createAllPayInfo.provinces }}</div>
              <div>注册城市代码 : {{ openAccountInfo.createAllPayInfo.city }}</div>
              <div>注册区县代码 : {{ openAccountInfo.createAllPayInfo.district }}</div>
              <div>客服电话 : {{ openAccountInfo.createAllPayInfo.customerPhone }}</div>
              <div>营业执照签发日 : {{ openAccountInfo.createAllPayInfo.licenseTermStart }}</div>
              <div>营业执照到期日 : {{ openAccountInfo.createAllPayInfo.licenseTermEnd }}</div>
            </el-collapse-item>
            <el-collapse-item title="法人信息">
              <div>法人手机号 : {{ openAccountInfo.createAllPayInfo.corporatePhone }}</div>
              <div>法人证件签发日 : {{ openAccountInfo.createAllPayInfo.idCardLegalTermStart }}</div>
              <div>法人证件到期日 : {{ openAccountInfo.createAllPayInfo.idCardLegalTermEnd }}</div>
              <div>法人国籍 : {{ openAccountInfo.createAllPayInfo.citizenship }}</div>
              <div>法人性别 : {{ openAccountInfo.createAllPayInfo.sex }}</div>
              <div>法人户籍地址 : {{ openAccountInfo.createAllPayInfo.domicileAddress }}</div>
              <div>法人证件签发地址 : {{ openAccountInfo.createAllPayInfo.issuingAddress }}</div>
            </el-collapse-item>
            <el-collapse-item title="联系人信息">
              <div>联系人名称 : {{ openAccountInfo.createAllPayInfo.corporatePhone }}</div>
              <div>联系人电话 : {{ openAccountInfo.createAllPayInfo.contactPhone }}</div>
              <div>联系人邮箱 : {{ openAccountInfo.createAllPayInfo.contactEmail }}</div>
              <div>联系人联系地址 : {{ openAccountInfo.createAllPayInfo.contactAddress }}</div>
              <div>联系人省份代码 : {{ openAccountInfo.createAllPayInfo.contactProvCd }}</div>
              <div>联系人城市代码 : {{ openAccountInfo.createAllPayInfo.contactCityCd }}</div>
              <div>联系人区县代码 : {{ openAccountInfo.createAllPayInfo.contactContryCd }}</div>
            </el-collapse-item>
            <el-collapse-item title="开卡人信息">
              <div>开卡人名称 : {{ openAccountInfo.createAllPayInfo.accountName }}</div>
              <div>卡号 : {{ openAccountInfo.createAllPayInfo.acct }}</div>
              <div>开户行银行简称 : {{ openAccountInfo.createAllPayInfo.bankCd }}</div>
              <div>开户支行号 : {{ openAccountInfo.createAllPayInfo.branchBankCd }}</div>
              <div>开户支行名称 : {{ openAccountInfo.createAllPayInfo.branchBankName }}</div>
              <div>银行预留手机号 : {{ openAccountInfo.createAllPayInfo.mobileNo }}</div>
            </el-collapse-item>
            <el-collapse-item title="控股股东或者实际控制人信息">
              <div>控股股东或者实际控制人 : {{ openAccountInfo.createAllPayInfo.controllerName }}</div>
              <div>控股股东或者实际控制人证件种类 : {{ openAccountInfo.createAllPayInfo.controllerType }}</div>
              <div>控股股东或者实际控制人证件号码 : {{ openAccountInfo.createAllPayInfo.controllerNumber }}</div>
              <div>控股股东或实际控制人身份证签发日 : {{ openAccountInfo.createAllPayInfo.controllerStart }}</div>
              <div>控股股东或实际控制人身份证到期日 : {{ openAccountInfo.createAllPayInfo.controllerEnd }}</div>
            </el-collapse-item>
            <el-collapse-item title="受益人信息">
              <div>受益人名称 : {{ openAccountInfo.createAllPayInfo.favoreeName }}</div>
              <div>受益所有人身份证件种类 : {{ openAccountInfo.createAllPayInfo.favoreeType }}</div>
              <div>受益所有人身份证件号码 : {{ openAccountInfo.createAllPayInfo.favoreeNumber }}</div>
              <div>受益所有人身份证件签发日期 : {{ openAccountInfo.createAllPayInfo.favoreeStart }}</div>
              <div>受益所有人身份证件到期日期 : {{ openAccountInfo.createAllPayInfo.favoreeEnd }}</div>
              <div>受益所有人身份证件签发地址 : {{ openAccountInfo.createAllPayInfo.favoreeAddress }}</div>
            </el-collapse-item>
            <el-collapse-item title="支付功能开通情况">
              <div :key="openPay.powerId" v-for="openPay in openAccountInfo.createAllPayInfo.openPayList" style="margin: 8px;">
                <div>功能ID : {{ openPay.powerId }}</div>
                <div>费率 : {{ openPay.rate }}</div>
                <div>状态 : {{ openPay.status }}</div>
                <div>创建时间 : {{ openPay.createTime }}</div>
                <div>更新时间 : {{ openPay.updateTime }}</div>
              </div>
            </el-collapse-item>

            <el-collapse-item title="图片信息">
              <div style="display: flex; flex-wrap: wrap;">
                <div class="demo-image__preview" style="margin: 5px;">
                  <el-image title="门头照" style="width: 100px; height: 100px"
                            :src="openAccountInfo.createAllPayInfo.shopEntrancePic" :preview-src-list="createAllPayPicUrlList"/>
                </div>`
                <div class="demo-image__preview" style="margin: 5px;">
                  <el-image title="营业执照" style="width: 100px; height: 100px"
                            :src="openAccountInfo.createAllPayInfo.businessLicensePic" :preview-src-list="createAllPayPicUrlList"/>
                </div>
                <div class="demo-image__preview" style="margin: 5px;">
                  <el-image title="开户许可证" style="width: 100px; height: 100px"
                            :src="openAccountInfo.createAllPayInfo.openAccountPic" :preview-src-list="createAllPayPicUrlList"/>
                </div>
                <div class="demo-image__preview" style="margin: 5px;">
                  <el-image title="法人身份证正面" style="width: 100px; height: 100px"
                            :src="openAccountInfo.createAllPayInfo.idCardLegalZPic" :preview-src-list="createAllPayPicUrlList"/>
                </div>
                <div class="demo-image__preview" style="margin: 5px;">
                  <el-image title="法人身份证反面" style="width: 100px; height: 100px"
                            :src="openAccountInfo.createAllPayInfo.idCardLegalFPic" :preview-src-list="createAllPayPicUrlList"/>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <el-card v-loading="loading" class="box-card" v-if="openAccountInfo.thirdUserInfo != undefined">
          <div slot="header" class="clearfix">
            <span>三方({{ formModel.channelProduct }})用户信息</span>
            <el-button style="float: right; padding: 3px 0" type="text">修改</el-button>
          </div>
          <div class="text item">平台用户ID : {{ openAccountInfo.thirdUserInfo.userId }}</div>
          <div class="text item">第三方类型 : {{ openAccountInfo.thirdUserInfo.thirdType }}</div>
          <div class="text item">用户类型（对公、对私） : {{ openAccountInfo.thirdUserInfo.userType }}</div>
          <div class="text item">第三方账号 : {{ openAccountInfo.thirdUserInfo.accountNo }}</div>
          <div class="text item">结算银行卡开户行 : {{ openAccountInfo.thirdUserInfo.settleBank }}</div>
          <div class="text item">创建时间 : {{ openAccountInfo.thirdUserInfo.ctime }}</div>
          <div class="text item">最后更新时间 : {{ openAccountInfo.thirdUserInfo.utime }}</div>
          <div class="text item">业务线类型 : {{ openAccountInfo.thirdUserInfo.businessIdType }}</div>
          <div class="text item">第三方用户id : {{ openAccountInfo.thirdUserInfo.thirdUserId }}</div>
          <div class="text item">状态 : {{ openAccountInfo.thirdUserInfo.status }}</div>
          <div class="text item">通知业务方成功地址 : {{ openAccountInfo.thirdUserInfo.callbackAddress }}</div>
          <div class="text item">第三方结算账号 : {{ openAccountInfo.thirdUserInfo.settleAccount }}</div>
        </el-card>

        <el-card v-loading="loading" class="box-card" v-if="openAccountInfo.applyOpenAccount != undefined">
          <div slot="header" class="clearfix">
            <span>微信申请单信息</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="openApplyDialog">重新申请</el-button>
          </div>
          <div class="text item">申请单号: {{ openAccountInfo.applyOpenAccount.applyCode }}</div>
          <div class="text item">用户id : {{ openAccountInfo.applyOpenAccount.accountId }}</div>
          <div class="text item">三方用户id : {{ openAccountInfo.applyOpenAccount.thirdUserId }}</div>
          <div class="text item">微信申请单号 : {{ openAccountInfo.applyOpenAccount.applymentId }}</div>
          <div class="text item">状态 : {{ openAccountInfo.applyOpenAccount.status }}</div>
          <div class="text item">是否授权 : {{ openAccountInfo.applyOpenAccount.authorize }}</div>
          <div class="text item">驳回原因 : {{ openAccountInfo.applyOpenAccount.rejectReason }}</div>
          <div class="text item">创建时间 : {{ openAccountInfo.applyOpenAccount.createTime }}</div>
          <div class="text item">更新时间 : {{ openAccountInfo.applyOpenAccount.updateTime }}</div>
        </el-card>

      </div>
    </ll-list-page-layout>

    <el-dialog title="重新申请开户意愿申请单" :visible.sync="open" width="500px" append-to-body v-if="openAccountInfo != undefined">
      <el-form ref="openAccountApplyForm" label-width="80px" v-if="openAccountInfo.applyOpenAccount != undefined">
        <div class="text item">申请单号: {{ openAccountInfo.applyOpenAccount.applyCode }}</div>
        <div class="text item">用户id : {{ openAccountInfo.applyOpenAccount.accountId }}</div>
        <div class="text item">三方用户id : {{ openAccountInfo.applyOpenAccount.thirdUserId }}</div>
        <div class="text item">微信申请单号 : {{ openAccountInfo.applyOpenAccount.applymentId }}</div>
        <div class="text item">状态 : {{ openAccountInfo.applyOpenAccount.status }}</div>
        <div class="text item">是否授权 : {{ openAccountInfo.applyOpenAccount.authorize }}</div>
        <div class="text item">驳回原因 : {{ openAccountInfo.applyOpenAccount.rejectReason }}</div>
        <div class="text item">创建时间 : {{ openAccountInfo.applyOpenAccount.createTime }}</div>
        <div class="text item">更新时间 : {{ openAccountInfo.applyOpenAccount.updateTime }}</div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitOpenAccountApplyForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="支付功能重新开通" :visible.sync="openPayOpen" width="600px" @close="cancel">
      <el-form ref="openPayForm" label-width="80px">
        <div style="margin: 5px 0;" v-if="openPayPowerIds != undefined">
          <label :key="powerId.key" v-for="powerId in openPayPowerIds" style="margin: 10px;">
            <label>{{ powerId.key }} : {{ powerId.label }}  </label>
          </label>
        </div>
        <div style="margin: 10px 0;" v-if="openAccountInfo != undefined
                                          && openAccountInfo.createAllPayInfo != undefined
                                          && openAccountInfo.createAllPayInfo.openPayList != undefined">
          <label :key="openPay.powerId" v-for="openPay in openAccountInfo.createAllPayInfo.openPayList" style="margin: 8px;">
            <label>{{ openPay.powerId }} : {{ openPay.status }}  </label>
          </label>
        </div>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <el-checkbox-group v-model="checkedOpenPayList" @change="handleCheckedCitiesChange">
          <el-checkbox v-for="openPay in openPayList" :label="openPay" :key="openPay">{{openPay}}</el-checkbox>
        </el-checkbox-group>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitOpenPayForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog title="用户基本信息修改" :close-on-click-modal = "false" :visible.sync="updateUserInfoOpen" width="1200px">
      <el-form ref="updateUserInfoForm" :inline="true" label-width="80px" v-if="updateUserInfo != undefined" :model="updateUserInfo" size="mini">
        <el-form-item label="公司名称">
          <el-input v-model="updateUserInfo.name"/>
        </el-form-item>
        <el-form-item label="电话号码">
          <el-input v-model="updateUserInfo.mobile"/>
        </el-form-item>
        <el-form-item label="办公地址">
          <el-input v-model="updateUserInfo.address"/>
        </el-form-item>
        <el-form-item label="业务类型">
          <el-select v-model="updateUserInfo.businessIdType" placeholder="请选择">
            <el-option
              v-for="item in formItem[1].attrs['options']"
              :key="item.key"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-divider content-position="left">认证信息</el-divider>
        <el-form-item label="证件号码">
          <el-input v-model="updateUserInfo.cardId"/>
        </el-form-item>
        <el-form-item label="证件类型">
          <el-select v-model="updateUserInfo.identityType" placeholder="请选择">
            <el-option
              v-for="item in openAccountCertType"
              :key="item.key"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="起始日期">
          <el-date-picker
            v-model="updateUserInfo.validFrom"
            type="date"
            placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 160px"/>
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="updateUserInfo.validUntil"
            type="date"
            placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 160px"/>
        </el-form-item>
        <el-form-item label="行业">
          <el-input v-model="updateUserInfo.industry"/>
        </el-form-item>
        <el-form-item label="职业">
          <el-input v-model="updateUserInfo.profession"/>
        </el-form-item>
        <el-divider content-position="left">联系人信息 <el-button type="text" @click="addContractInfoToForm">新增联系人</el-button></el-divider>
        <div v-if="updateUserInfo.contactInfos != undefined">
          <el-form :inline="true" label-width="80px" :key="index" :model="updateUserInfo.contactInfos[index]" v-for="(contactInfo,index) in updateUserInfo.contactInfos" size="mini">
            <el-form-item label="userId">
              <el-input v-model="updateUserInfo.contactInfos[index].userId"/>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="updateUserInfo.contactInfos[index].contactType" placeholder="请选择">
                <el-option
                  v-for="item in openAccountContactType"
                  :key="item.key"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="名称">
              <el-input v-model="updateUserInfo.contactInfos[index].contactName"/>
            </el-form-item>
            <el-form-item label="证件类型">
              <el-select v-model="updateUserInfo.contactInfos[index].contactCertType" placeholder="请选择">
                <el-option
                  v-for="item in openAccountCertType"
                  :key="item.key"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="证件号">
              <el-input v-model="updateUserInfo.contactInfos[index].contactCertNo"/>
            </el-form-item>
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="updateUserInfo.contactInfos[index].contactCertValidFrom"
                type="date"
                placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 160px"/>
            </el-form-item>
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="updateUserInfo.contactInfos[index].contactCertValidUntil"
                type="date"
                placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 160px"/>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="updateUserInfo.contactInfos[index].contactCertMobile"/>
            </el-form-item>
            <el-button type="text" @click="reduceContractInfoToForm(index)">删除联系人</el-button>
          </el-form>
        </div>

        <el-divider content-position="left">结算信息</el-divider>
        <el-form-item label="卡号">
          <el-input v-model="updateUserInfo.account"/>
        </el-form-item>
        <el-form-item label="开户名">
          <el-input v-model="updateUserInfo.settleName"/>
        </el-form-item>
        <el-form-item label="预留手机">
          <el-input v-model="updateUserInfo.settleMobile"/>
        </el-form-item>
        <el-form-item label="银行名称">
          <el-input v-model="updateUserInfo.openBank"/>
        </el-form-item>
        <el-form-item label="卡号类型">
          <el-input v-model="updateUserInfo.cardType"/>
        </el-form-item>
        <el-form-item label="开户行名">
          <el-input v-model="updateUserInfo.subBank"/>
        </el-form-item>
        <el-form-item label="开户行号">
          <el-input v-model="updateUserInfo.subBankCode"/>
        </el-form-item>
        <el-form-item label="用户名称">
          <el-input v-model="updateUserInfo.businessUserName"/>
        </el-form-item>
        <el-form-item label="通知地址">
          <el-input v-model="updateUserInfo.callbackAddress"/>
        </el-form-item>

        <el-divider content-position="left">图片地址</el-divider>
        <el-form-item label="营业执照">
          <el-input  v-model="updateUserInfo.picMap['1']" style="width: 700px"/>
        </el-form-item>
        <el-form-item label="证件正面">
          <el-input  v-model="updateUserInfo.picMap['2']" style="width: 700px"/>
        </el-form-item>
        <el-form-item label="证件反面">
          <el-input  v-model="updateUserInfo.picMap['3']" style="width: 700px"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpdateUserInfoForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDictMapQuery } from '@/api/common'
import { queryOpenAccountInfo, openAccountApplyment, retryOpenPay, updateUserInfo } from '@/api/openAccount'
export default {
  data() {
    return {
      open: false,
      openPayOpen: false,
      updateUserInfoOpen: false,
      loading: true,
      formModel: {
        accountId: '',
        businessId: '',
        businessIdType: 'ec_pop',
        channelProduct: 'qifutong', //渠道产品
      },
      formItem: [
        {
          label: '渠道产品',
          prop: 'channelProduct',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '业务类型',
          prop: 'businessIdType',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
      ],
      openAccountInfo: undefined,
      formRules: {
        accountId: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ],
        businessId: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ],
      },
      userInfoPicUrls:[],
      userInfoPicUrlList:[],
      createAllPayPicUrlList:[],
      checkAll: false,
      checkedOpenPayList: [],
      openPayList: [],
      openAccountContactType: [],
      openAccountCertType: [],
      openPayPowerIds: [],
      isIndeterminate: false,
      updateUserInfo: undefined,
    };
  },
  created() {
    getDictMapQuery("channelProductTypeMap","map").then(response => {
      this.formItem[0].attrs['options'] = response.result;
    });
    getDictMapQuery("businessIdTypeMap","map").then(response => {
      this.formItem[1].attrs['options'] = response.result;
    });
    getDictMapQuery("fbankPowerIdTypeMap","map").then(response => {
      this.openPayList = response.result.map(item => {
        return item.label;
      });
      this.openPayPowerIds = response.result;
    });
    getDictMapQuery("openAccountContactTypeMap","map").then(response => {
      this.openAccountContactType = response.result;
    });
    getDictMapQuery("openAccountCertTypeMap","map").then(response => {
      this.openAccountCertType = response.result;
    });
  },
  mounted() {},
  methods: {
    // 查询
    handleFormSubmit() {
      this.loading = true;
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          let data = {'accountId': this.formModel.accountId,'businessId': this.formModel.businessId,'businessIdType': this.formModel.businessIdType, 'channelProduct': this.formModel.channelProduct};
          queryOpenAccountInfo(data).then(response => {
            this.openAccountInfo = response.result;
            this.userInfoPicUrls = [];
            this.userInfoPicUrlList = [];
            for(var item in this.openAccountInfo.userInfo.picMap) {
              switch (item) {
                case "1":
                  this.userInfoPicUrls.push({ key:"企业证件照片", url:this.openAccountInfo.userInfo.picMap["1"]});
                  break;
                case "2":
                  this.userInfoPicUrls.push({ key:"法人身份证正面", url:this.openAccountInfo.userInfo.picMap["2"]});
                  break;
                case "3":
                  this.userInfoPicUrls.push({ key:"法人身份证背面", url:this.openAccountInfo.userInfo.picMap["3"]});
                  break;
                case "4":
                  this.userInfoPicUrls.push({ key:"个人身份证正面", url:this.openAccountInfo.userInfo.picMap["4"]});
                  break;
                case "5":
                  this.userInfoPicUrls.push({ key:"个人身份证背面", url:this.openAccountInfo.userInfo.picMap["5"]});
                  break;
                case "6":
                  this.userInfoPicUrls.push({ key:"企业门头照图片", url:this.openAccountInfo.userInfo.picMap["6"]});
                  break;
                case "7":
                  this.userInfoPicUrls.push({ key:"开户许可证", url:this.openAccountInfo.userInfo.picMap["7"]});
                  break;
                case "8":
                  this.userInfoPicUrls.push({ key:"经营场所图片", url:this.openAccountInfo.userInfo.picMap["8"]});
                  break;
              }
            }
            this.userInfoPicUrls.forEach(item => {
                this.userInfoPicUrlList.push(item.url);
            });

            this.createAllPayPicUrlList.push(this.openAccountInfo.createAllPayInfo.shopEntrancePic);
            this.createAllPayPicUrlList.push(this.openAccountInfo.createAllPayInfo.businessLicensePic);
            this.createAllPayPicUrlList.push(this.openAccountInfo.createAllPayInfo.openAccountPic);
            this.createAllPayPicUrlList.push(this.openAccountInfo.createAllPayInfo.idCardLegalZPic);
            this.createAllPayPicUrlList.push(this.openAccountInfo.createAllPayInfo.idCardLegalFPic);

            this.loading = false;
          }).catch(error => {
            console.log(error);
            this.loading = false;
          });
        } else {
          this.loading = false;
          return false
        }
      })
    },
    validateRequiredItem(rule, value, callback) {
      let errorMsg = ''
      if (!this.validateParamsAllEmpty()) {
        errorMsg = 'ID不能同时为空'
        callback(new Error(errorMsg))
      }
      callback()
    },
    validateParamsAllEmpty(){
      const {
        accountId, channelProduct, businessId
      } = this.formModel
      if((accountId == '' && businessId == '') || channelProduct == '') {
        return false;
      }else {
        return true;
      }
    },
    cancel() {
      this.open = false;
      this.openPayOpen = false;
      this.updateUserInfoOpen = false;
      this.checkedOpenPayList = [];
    },
    submitOpenAccountApplyForm() {
      // 开户意愿申请
      try {
        let data = {
          'applyCode': this.openAccountInfo.applyOpenAccount.applyCode,
          'accountId': this.openAccountInfo.applyOpenAccount.accountId,
          'thirdUserId': this.openAccountInfo.applyOpenAccount.thirdUserId,
        }
        openAccountApplyment(data).then(response => {
          if(response.code == 1000) {
            this.$notify({
              title: '提示',
              message: "申请成功!",
              showClose: false,
              type: 'success',
              duration: 2000
            });
          }else{
            this.$notify({
              title: '提示',
              message: "申请失败!",
              showClose: false,
              type: 'error',
              duration: 2000
            });
          }
        })
      } finally {
        this.cancel()
        this.handleFormSubmit();
      }
    },
    submitOpenPayForm() {
      // 支付功能重新开通
      try {
        let data = {
          'accountId': this.formModel.accountId || this.openAccountInfo.createAllPayInfo.userId,
          'channelProduct': this.formModel.channelProduct,
          'powerIds': this.checkedOpenPayList,
        }
        retryOpenPay(data).then(response => {
          if(response.code == 1000) {
            this.$notify({
              title: '提示',
              message: "开通成功!",
              showClose: false,
              type: 'success',
              duration: 2000
            });
          }else{
            this.$notify({
              title: '提示',
              message: "开通失败!",
              showClose: false,
              type: 'error',
              duration: 2000
            });
          }

          this.handleFormSubmit();
        })
      } finally {
        this.cancel()
      }
    },
    submitUpdateUserInfoForm() {
      // 更新用户信息
      try {
        let data = this.updateUserInfo;
        updateUserInfo(data).then(response => {
          if(response.code == 1000) {
            this.$notify({
              title: '提示',
              message: "修改成功!",
              showClose: false,
              type: 'success',
              duration: 2000
            });
          }else{
            this.$notify({
              title: '提示',
              message: "修改失败!",
              showClose: false,
              type: 'error',
              duration: 2000
            });
          }
        })
      } finally {
        this.cancel()
        this.handleFormSubmit();
      }
    },
    openApplyDialog() {
      this.open = true;
    },
    openUpdateUserInfoDialog() {
      this.updateUserInfoOpen = true;
      this.updateUserInfo = this.openAccountInfo.userInfo;
    },
    handleCheckAllChange(val) {
      this.checkedOpenPayList = val ? this.openPayList : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.openPayList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.openPayList.length;
    },
    addContractInfoToForm() {
      this.updateUserInfo.contactInfos.push({
        'userId':'',
        'contactType':'',
        'contactName':'',
        'contactCertType':'',
        'contactCertNo':'',
        'contactCertValidFrom':'',
        'contactCertValidUntil':'',
        'contactCertMobile':'',
      });
    },
    reduceContractInfoToForm(index) {
      this.updateUserInfo.contactInfos.splice(index, 1);
    },
  }
}
</script>

<style lang="scss" scoped>
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}

.box-card {
  width: 480px;
  margin: 10px;
}
</style>
