<template>
    <div class="finance-stagement mt10">
        <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
            <div class="top">
                <div style="font-size:24px;font-weight: bold;display: flex;justify-content: space-between;">
                <div>查询还款明细</div>
                <el-button type="primary" @click="callBack">返回</el-button>
                </div>
                <div style="display: flex;margin-top:20px;justify-content: flex-start;">
                <div><span>客户名称：{{ merchantName }}</span></div>
                <div style="margin-left:20px;"><span>借据号：{{ loanNo }}</span></div>
                </div>  
            </div>
            <el-table
                ref="table"
                v-loading="loading"
                :data="dataTable"
                stripe
                border
                highlight-current-row
                :header-cell-style="{background: 'rgb(239, 241, 245)'}"
                style="width: 100%"
                class="lwq"
                >
                <el-table-column
                    v-for="(item) in tableColumns"
                    :key="item.prop"
                    :label="item.label"
                    :width="item.width"
                    :prop="item.prop"
                    :formatter="item.formatter"
                    show-overflow-tooltip
                    align="center"
                />
            </el-table>
        </ll-list-page-layout>
    </div>
</template>

<script>
import {getRepayPlandDetail} from "@/api/jdBill.js";
import {Message} from "element-ui"
  // 引入处理时间戳
  import moment from 'moment'
export default {
    data() {
        return {
            dataTable:[], // 表格数据
            loading:false,
            fullscreenLoading: false,
            payNo:'',
            loanNo:"",
            merchantName:"",
            tableColumns: [
                {
                    prop: 'merchantName',
                    label: '客户名称',
                    width: '200'
                },
                {
                    prop: 'loanArNo',
                    label: '借据号',
                    width: '200'
                },
                {
                    prop: 'orderNo',
                    label: '订单编号',
                    width: '200'
                },
                {
                    prop: 'repayTime',
                    label: '还款日期',
                    width: '200',
                    formatter:(record) =>
                     record.repayTime ? moment(record.repayTime).format('YYYY-MM-DD HH:mm:ss'):'--'
                },
                {
                    prop: 'refundNo',
                    label: '退款支付单号',
                    width: '200'
                },
                {
                    prop: 'businessRefundNo',
                    label: '商城退款单号',
                    width: '200'
                },
                {
                    prop: 'totalAmt',
                    label: '本次已还总额',
                    width: '200'
                },
                {
                    prop: 'paidPrinBal',
                    label: '本次已还本金',
                    width: '200'
                },
                {
                    prop: 'paidIntBal',
                    label: '本次已还利息',
                    width: '200'
                },
                {
                    prop: 'paidOtherAmount',
                    label: '本次已还其它',
                    width: '200'
                },
                {
                    prop: 'paidPenInt',
                    label: '本次已还罚息',
                    width: '200'
                },
                {
                    prop: 'paidCoreInt',
                    label: '本次核企补贴已还利息',
                    width: '200'
                },
                {
                    prop: 'paidCustInt',
                    label: '本次客户自担已还利息',
                    width: '200'
                }
            ]
        }
    },
    created(){
      if (this.$route.query.payNo) {
          this.payNo = this.$route.query.payNo;
          this.loanNo = this.$route.query.loanNo
          this.merchantName = this.$route.query.merchantName
          this.getDetailData(this.$route.query.loanNo);
        }
    },
    methods: {
        callBack(){
            // 先获取当前路由
            const view = this.$route;
            // 调用 store 的 action 关闭当前标签
            this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
                // 跳转回列表页
                this.$router.push('/jdBill/repayPlan');
            });
        },
        /**获取详情 */
        getDetailData(payNo){
            const params = {
                loanArNo: payNo
            }
            getRepayPlandDetail(params).then(response => {
            let { result,code,msg} = response
            if(code == '1000'){
                this.dataTable = result;
            }else{
                Message({
                    message: msg,
                    type: "error",
                    duration: 5 * 1000
                });
            }
            }).catch(() => {
            })
        },
    }
}
</script>

<style lang="scss" scoped>
    .top{
        margin-bottom: 50px;
    }
  .item{
    display: flex;
    .itemOne {
        flex-grow:0.1;
    }
  }
  .finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
</style>