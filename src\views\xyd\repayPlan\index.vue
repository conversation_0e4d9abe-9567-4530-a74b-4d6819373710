<template>
    <div class="finance-stagement mt10">
      <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
        <el-form
          ref="searchForm"
          slot="search-form"
          class="statement-search-form"
          label-width="120px"
          :inline="true"
          :model="formModel"
          >
          <!-- :rules="formRules" -->
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="借据单号" label-width="90px" prop="loanNo">
              <el-input v-model.trim="formModel.loanNo" placeholder="请输入借据单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单号" label-width="90px" prop="orderNo">
              <el-input v-model.trim="formModel.orderNo" placeholder="请输入订单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付单号" label-width="90px" prop="payNo">
              <el-input v-model.trim="formModel.payNo" placeholder="请输入支付单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付时间" label-width="100px" prop="payDay">
              <el-date-picker
                v-model="payDay"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="timestamp"
                :range-separator="'至'"
                :clearable="true"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="validateFormRequiredItem"
                >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="是否结清" label-width="90px" prop="paidOff">
              <el-select v-model="formModel.paidOff" placeholder="请选择是否结清">
                <el-option label="全部" value="" />
                <el-option label="结清" :value=1 />
                <el-option label="未结清" :value=0 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户编码" label-width="90px" prop="merchantId">
              <el-input v-model.trim="formModel.merchantId" placeholder="请输入客户编码" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称" label-width="90px" prop="merchantName">
                <el-input v-model.trim="formModel.merchantName" placeholder="请输入客户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商户编码" label-width="90px" prop="orgId">
              <el-input v-model.trim="formModel.orgId" placeholder="请输入商户编码" class="samewidth" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
            <el-col :span="6">
            <el-form-item label="商户名称" label-width="90px" prop="orgName">
              <el-input v-model.trim="formModel.orgName" placeholder="请输入商户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否包含原路退" label-width="110px" prop="isOriginalRoute">
              <el-select v-model="formModel.isOriginalRoute" placeholder="请选择是否包含原路退" style="width:195px">
                <el-option label="全部" value="" />
                <el-option label="是" :value=1 />
                <el-option label="否" :value=0 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实际还款日" label-width="100px" prop="duePay">
              <el-date-picker
                v-model="duePay"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="timestamp"
                :range-separator="'至'"
                :clearable="true"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="dueStartDateActual"
                >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <div style="float:right;display: flex;justify-content: flex-end;">
              <ll-button type="success" @click="handleDetail">查询还款明细</ll-button>
              <ll-button type="primary" @click="handleResetForm">重置</ll-button>
              <ll-button type="primary" @click="handleGetList">查询</ll-button>
              <ll-button type="primary" @click="handleExportFile">导出</ll-button>
            </div>
          </el-col>
        </el-row>
        </el-form>
        <ll-data-grid
          ref="dataGrid"
          :pagination-page-size.sync="pagination.pageSize"
          :pagination-page-sizes="pagination.sizes"
          :table-columns="tableColumns"
          :async-fetch-data-fun="fetchData"
          @row-click="handleRowClick"
          :row-class-name="tableRowClassName"
        />
      </ll-list-page-layout>

      <!-- 导出弹窗 -->
      <el-dialog
        title="导出明细"
        :visible.sync="exportDialogVisible"
        width="500px"
        :close-on-click-modal="false"
      >
        <div class="export-dialog-content">
          <div class="export-tip">请选择支付时间所在月份</div>
          <div class="date-pickers">
            <div class="picker-item">
              <span class="picker-label">年份：</span>
              <el-date-picker
                v-model="exportYear"
                type="year"
                placeholder="选择年份"
                value-format="yyyy"
                style="width: 150px;"
              >
              </el-date-picker>
            </div>
            <div class="picker-item">
              <span class="picker-label">月份：</span>
              <el-select
                v-model="exportMonth"
                placeholder="选择月份"
                style="width: 150px;"
              >
                <el-option
                  v-for="month in 12"
                  :key="month"
                  :label="month.toString().padStart(2, '0')"
                  :value="month.toString().padStart(2, '0')"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExport">确认导出</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import {getjdCredit,exportRepayPlanList} from '@/api/xyd.js'
  import { parseTime, download } from '@/utils/index.js'
  import qs from 'qs';
  // 引入处理时间戳
  import moment from 'moment'
  export default {
    name: 'XydRepayPlan',
    data() {
      return {
        qs,
        payDay:[],
        duePay:[],
        actRepayDate:[], //实际还款日
        curRepayDate:[], //应还款日
        selectedRows: [], // 添加选中行的数据
        formModel: {
            loanNo: '', // 借据单号
            orderNo: '', //订单号
            payNo:'',//支付单号
            payStartTime:'',// 支付开始日期
            payEndTime:'',// 支付结束日期
            paidOff:"", //是否结清 1结清 0 未结清
            isOriginalRoute:"", //是否包含原路退
            merchantId:'',//客户编码
            merchantName:'',//客户名称
            orgId:'',//商户编码
            orgName:'',//商户名称
            dueStartDateActual:"", //实际开始还款日
            dueEndDateActual:"", //实际结束还款日
        },
        tableColumns: [
          {
            prop: 'outerOrderNo',
            label: '平台客户授信编号',
            'min-width': '200',
          },
          {
            prop: 'loanNo',
            label: '借据号',
            'min-width': '200',
          },
          {
            prop: 'orderNo',
            label: '订单号',
            'min-width': '200'
          },
          {
            prop: 'payNo',
            label: '支付单号',
            'min-width': '200'
          },
          {
            prop: 'loanAmount',
            label: '贷款金额',
            'min-width': '200'
          },
          {
            prop: 'interest',
            label: '贷款利息',
            'min-width': '200'
          },
          {
            prop: 'creditRate',
            label: '年利率',
            'min-width': '200'
          },
          {
            prop: 'loanLimit',
            label: '贷款期限',
            'min-width': '200'
          },
          {
            prop: 'payTime',
            label: '支付时间',
            'min-width': '150',
            formatter:(record) =>
            record.payTime ? moment(record.payTime).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'loanTime',
            label: '起息日',
            'min-width': '200'
          },
          {
            prop: 'dueDate',
            label: '应还款日',
            'min-width': '200'
          },
          {
            prop: 'dueDateActual',
            label: '实际还款日',
            'min-width': '200'
          },
          {
            prop: 'paidOff',
            label: '是否结清',
            'min-width': '200'
          },
          {
            prop: 'orgId',
            label: '商户编码',
            'min-width': '200'
          },
          {
            prop: 'orgName',
            label: '商户名称',
            'min-width': '200'
          },
          {
            prop: 'merchantId',
            label: '客户编码',
            'min-width': '200'
          },
          {
            prop: 'merchantName',
            label: '客户名称',
            'min-width': '200'
          },
          {
            prop: 'totalPlan',
            label: '期数',
            'min-width': '200'
          },
          {
            prop: 'principalPaid',
            label: '该期实付本金',
            'min-width': '200'
          },
          {
            prop: 'principal',
            label: '该期未付本金',
            'min-width': '200'
          },
          {
            prop: 'interestPaid',
            label: '该期实付利息',
            'min-width': '200'
          },
          {
            prop: 'currInterest',
            label: '该期未付利息',
            'min-width': '200'
          },
          {
            prop: 'subsidyInterest',
            label: '平台贴息金额',
            'min-width': '200'
          },
          {
            prop: 'subsidyDays',
            label: '平台贴息天数',
            'min-width': '200'
          },
          {
            prop: 'lateFeePaid',
            label: '该期实付逾期费',
            'min-width': '200'
          },
          {
            prop: 'lateFee',
            label: '该期未付逾期费',
            'min-width': '200'
          },
          {
            prop: 'dayInDefault',
            label: '逾期天数',
            'min-width': '200'
          },
          {
            prop: 'repayPenalty',
            label: '已还罚息',
            'min-width': '200'
          },
          {
            prop: 'isOriginalRoute',
            label: '是否包含原路退',
            'min-width': '200'
          },
        ],
        pagination: {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        fullscreenLoading: false,
        currentRow: null,
        exportDialogVisible: false,
        exportYear: new Date().getFullYear().toString(),
        exportMonth: (new Date().getMonth() + 1).toString().padStart(2, '0'),
      }
    },
    created(){
      this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
      this.formModel.payStartTime = this.payDay[0]
      this.formModel.payEndTime = this.payDay[1]
      this.duePay = []
      this.formModel.dueStartDateActual = ''
      this.formModel.dueEndDateActual = ''
    },
    activated() {
      // 组件被激活时重新加载数据
      this.$refs['dataGrid'] && this.$refs['dataGrid'].loadData()
    },
    computed: {
    },
    mounted() {},
    methods: {
      /** 跳转借据详情 */
      handleRowClick(row) {
        this.currentRow = row;
      },
      tableRowClassName({row}) {
        if (this.currentRow && row.payNo === this.currentRow.payNo) {
          return 'current-row';
        }
        return '';
      },
      handleDetail() {
        if (!this.currentRow) {
          this.$message({
            message: '请先选择要查看的数据',
            type: 'warning'
          });
          return;
        }
        
        // 跳转到详情页
        this.$router.replace({
          path: '/xyd/repayPlan/detail',
          query: {
            payNo: this.currentRow.payNo,
            merchantName:this.currentRow.merchantName,
            loanNo:this.currentRow.loanNo
          }
        });
      },
      lastMonthFirstDay(){
        const date = new Date()
        date.setMonth(date.getMonth() -1);//获取上一个月
        date.setDate(1);//设置为1号
        return date.getTime();
      },
      // 订单支付时间
      validateFormRequiredItem(val) {
        if (val) {
          this.formModel.payStartTime = val[0]
          this.formModel.payEndTime = val[1]
        } else {
          this.formModel.payStartTime = ''
          this.formModel.payEndTime = ''
        }
      },
      //实际还款时间
      dueStartDateActual(val){
        if (val) {
          this.formModel.dueStartDateActual = val[0]
          this.formModel.dueEndDateActual = val[1]
        } else {
          this.formModel.dueStartDateActual = ''
          this.formModel.dueEndDateActual = ''
        }
      },
      handleGetList() {
        this.$refs['dataGrid'].loadData()
      },
      fetchData({pagination}) {
        console.log(pagination);
        const { pageSize, currentPage } = pagination;
        const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
        return new Promise(resolve => {
          getjdCredit(params)
            .then(response => {
              let { data,totalCount } = response
              let tableData = {
                list: data,
                pagination: {
                  pageSize: pageSize,
                  total: totalCount
                }
              }
              resolve(tableData)
            })
            .catch(() => {
              resolve({
                list: [],
                pagination: { pageSize: 10, total: 0 }
              })
            })
        })
      },
      formatterTableData(tableData, pagination, totalCount) {
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
      handleResetForm() {
        this.$refs.searchForm.resetFields()
        this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
        this.duePay = []
        this.formModel = {
            loanNo: '', // 借据单号
            orderNo: '', //订单号
            payNo:'',//支付单号
            payStartTime:this.payDay[0],// 支付开始日期
            payEndTime:this.payDay[1],// 支付结束日期
            paidOff:"", //是否结清 1结清 0 未结清
            isOriginalRoute:"", //是否包含原路退
            merchantId:'',//客户编码
            merchantName:'',//客户名称
            orgId:'',//商户编码
            orgName:'',//商户名称
            dueStartDateActual:'', //实际开始还款日
            dueEndDateActual:'', //实际结束还款日
        },
        this.pagination = {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        this.handleGetList()
      },
        // 导出excel
        handleExportFile() {
          this.exportDialogVisible = true;
        },
        confirmExport() {
          if (!this.exportYear || !this.exportMonth) {
            this.$message.warning('请选择年份和月份');
            return;
          }
          // 使用选择的年月创建日期对象
          const startDate = moment(`${this.exportYear}-${this.exportMonth}-01`);
          const endDate = moment(startDate).endOf('month');
          const params = {
            payStartTime: startDate.valueOf(), // 转换为时间戳
            payEndTime: endDate.valueOf()      // 转换为时间戳
          };
          // 调用原来的导出逻辑
          const exportName = '小雨点还款计划'
          exportRepayPlanList(params)
            .then(response => {
              let result = response.data
              const dateStr = parseTime(new Date(), '{y}{m}{d}')
              const defExportName = `${exportName}-${dateStr}.xlsx`
              const fileExportName =
                response && response.headers && response.headers['export-filename']
                  ? decodeURIComponent(response.headers['export-filename'])
                  : defExportName
              download(result, fileExportName)
            })
            .catch(error => {
              console.error('导出失败:', {
                error: error,
                message: error.message,
                response: error.response,
                stack: error.stack,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data
              });
              // 根据错误状态显示不同的错误信息
              const errorMsg = error.response?.data?.message 
                || error.response?.statusText 
                || error.message 
                || '导出失败，请稍后重试';
              this.$message.error(errorMsg);
            })
            .finally(() => {
              this.exportDialogVisible = false;
            })
        },
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
  ::v-deep .current-row {
    background-color: #ecf5ff !important;
  }
  ::v-deep .el-table__body tr:hover > td {
    background-color: transparent !important;
  }
  ::v-deep .el-dialog {
    .el-dialog__header {
      border-bottom: 1px solid #EBEEF5;
      padding: 15px 20px;
    }
    .el-dialog__body {
      padding: 15px 20px;
    }
  }
  .export-dialog-content {
    .export-tip {
      margin-bottom: 20px;
    }
    .date-pickers {
      display: flex;
      align-items: center;
      .picker-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .picker-label {
          margin-right: 10px;
          white-space: nowrap;
        }
      }
    }
  }
  </style>
  