<!-- 适用门店 -->
<template>
  <el-dialog :visible.sync="recordDialog" append-to-body top="3vh" width="800px" title="提现记录" center destroy-on-close @close="close">
    <ll-data-grid
      ref="dataGrid"
      :pagination-page-size.sync="pagination.pageSize"
      :pagination-page-sizes="pagination.sizes"
      :table-columns="tableColumns"
      :async-fetch-data-fun="fetchData"
    />
  </el-dialog>
</template>

<script>
import { querywithdrawalRecord } from '@/api/report'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      recordDialog: true,
      tableColumns: [
        {
          prop: 'accountName',
          label: '提现账户',
          width: '160'
        },
        {
          prop: 'withdrawalDate',
          label: '发起提现时间',
          width: '180'
        },
        {
          prop: 'amount',
          label: '提现金额',
          width: '150'
        },
        {
          prop: 'status',
          label: '到账状态',
          width: '100'
        },
        {
          prop: 'successDate',
          label: '到账时间',
          width: '170'
        }
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      }
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
    },
    // 格式化数据
    formatterTableData(tableData, pagination, totalCount) {
      console.log(tableData)
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        item.successDate = item.successDate === null ? '--' : item.successDate 
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },

    // 整理查询参数
    getFormParams(pagination) {
      let params = {
        accountId: this.row.accountId,
        page: pagination ? pagination.currentPage : null,
        pageSize: pagination ? pagination.pageSize : null
      }
      return params
    },

    //表格加载
    fetchData({ pagination }) {
      let params = this.getFormParams(pagination)
      return new Promise(resolve => {
        querywithdrawalRecord(params)
          .then(response => {
            let { data } = response
            let { totalCount } = response
            let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch((err) => {
            console.log(err)
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 20px;
  height: 536px;
}
.el-dialog__footer {
  .dialog-footer {
    display: block;
    min-height: 34px;
  }
}
</style>
