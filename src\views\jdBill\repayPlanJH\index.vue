<template>
    <div class="finance-stagement mt10">
        <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
            <el-form
                ref="searchForm"
                slot="search-form"
                class="statement-search-form"
                label-width="120px"
                :inline="true"
                :model="formModel"
                >
                <el-row :gutter="10">
                    <el-col :span="6">
                        <el-form-item label="客户编码" label-width="90px" prop="merchantId">
                        <el-input v-model.trim="formModel.merchantId" placeholder="请输入客户编码" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户名称" label-width="90px" prop="merchantName">
                            <el-input v-model.trim="formModel.merchantName" placeholder="请输入客户名称" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="账单月份" label-width="90px" prop="billMonth" style="width: 335px;">
                            <el-date-picker
                                style="display: inline-block;width: 108.6px;"
                                v-model="billMonth[0]"
                                type="month"
                                placeholder="选择月"
                                :picker-options="startPickerOptions"
                                @change="handleStartDateChange">
                            </el-date-picker>
                            -
                            <el-date-picker
                                style="display: inline-block;width: 108.6px;"
                                v-model="billMonth[1]"
                                type="month"
                                placeholder="选择月"
                                :picker-options="endPickerOptions"
                                @change="handleEndDateChange">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="还款状态" label-width="110px" prop="repayState">
                        <el-select v-model="formModel.repayState" placeholder="请选择还款状态" style="width:195px">
                            <el-option label="全部" value="" />
                            <el-option label="正常" :value=1 />
                            <el-option label="已结清" :value=0 />
                            <el-option label="逾期" :value=2 />
                        </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="6" :offset="17">
                        <div style="float:right;display: flex;justify-content: flex-end;">
                        <ll-button type="primary" @click="handleResetForm">重置</ll-button>
                        <ll-button type="primary" @click="handleGetList">查询</ll-button>
                        <ll-button type="primary" @click="confirmExport">导出</ll-button>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
            <ll-data-grid
            ref="dataGrid"
            :pagination-page-size.sync="pagination.pageSize"
            :pagination-page-sizes="pagination.sizes"
            :table-columns="tableColumns"
            :async-fetch-data-fun="fetchData"
            />
        </ll-list-page-layout>
    </div>
</template>

<script>
import { getjdBillCreditJH,exportjdBillCreditJH } from '@/api/jdBill.js'
import { parseTime, download } from '@/utils/index.js'
import moment from 'moment'
export default {
name:'repayPlanJH',
data() {
  return {
    fullscreenLoading: false,
    billMonth: [],
    formModel: {
        merchantId: '', // 客户编码
        merchantName: '',
        repayState:'', // 还款状态 1正常 0已结清 2逾期
        startBillMonth: '',
        endBillMonth: '',
    },
    // 开始日期选择器配置
    startPickerOptions: {
      disabledDate: (time) => {
        if (this.billMonth[1]) {
          return time.getTime() > this.billMonth[1].getTime()
        }
        return false
      }
    },
    // 结束日期选择器配置
    endPickerOptions: {
      disabledDate: (time) => {
        if (this.billMonth[0]) {
          return time.getTime() < this.billMonth[0].getTime()
        }
        return false
      }
    },
    pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 40, 50],
    },
    currentPage: {
      page: 1,
      sizes: 10
    },
    tableColumns: [
        {
            prop: 'creditNo',
            label: '授信编号',
            'min-width': '200'
        },
        {
            prop: 'merchantId',
            label: '客户编码',
            'min-width': '200'
        },
        {
            prop: 'merchantName',
            label: '客户名称',
            'min-width': '200'
        },
        {
            prop: 'billMonth',
            label: '账单月份',
            'min-width': '200'
        },
        {
            prop: 'intFreeRepayDate',
            label: '免息还款日',
            'min-width': '200',
            formatter:(record) =>
            record.intFreeRepayDate ? moment(record.intFreeRepayDate).format('YYYY-MM-DD HH:mm:ss'):'--'
        },
        {
            prop: 'termEndDate',
            label: '应还款日',
            'min-width': '200',
            formatter:(record) =>
            record.termEndDate ? moment(record.termEndDate).format('YYYY-MM-DD HH:mm:ss'):'--'
        },
        {
            prop: 'billRepayAmt',
            label: '账单待还金额',
            'min-width': '200',
            formatter: (record) => 
              record.billRepayAmt !== null && record.billRepayAmt !== undefined 
                  ? Number(record.billRepayAmt).toFixed(2) 
                  : '--'
        },
        {
            prop: 'repayTotalAmt',
            label: '应还总额',
            'min-width': '200',
            formatter: (record) => 
              record.repayTotalAmt !== null && record.repayTotalAmt !== undefined 
                  ? Number(record.repayTotalAmt).toFixed(2) 
                  : '--'
        },
        {
            prop: 'repayPrincipal',
            label: '应还本金',
            'min-width': '200',
            formatter: (record) => 
              record.repayPrincipal !== null && record.repayPrincipal !== undefined 
                  ? Number(record.repayPrincipal).toFixed(2) 
                  : '--'
        },
        {
            prop: 'repayTotalInterest',
            label: '应还总利息',
            'min-width': '200',
            formatter: (record) => 
              record.repayTotalInterest !== null && record.repayTotalInterest !== undefined 
                  ? Number(record.repayTotalInterest).toFixed(2) 
                  : '--'
        },
        {
            prop: 'payableCoreInt',
            label: '核企补贴应还利息',
            'min-width': '200',
            formatter: (record) => 
              record.payableCoreInt !== null && record.payableCoreInt !== undefined 
                  ? Number(record.payableCoreInt).toFixed(2) 
                  : '--'
        },
        {
            prop: 'payableCustInt',
            label: '客户自担应还利息',
            'min-width': '200',
            formatter: (record) => 
              record.payableCustInt !== null && record.payableCustInt !== undefined 
                  ? Number(record.payableCustInt).toFixed(2) 
                  : '--'
        },
        {
            prop: 'paidPrinBal',
            label: '当期已还本金',
            'min-width': '200',
            formatter: (record) => 
              record.paidPrinBal !== null && record.paidPrinBal !== undefined 
                  ? Number(record.paidPrinBal).toFixed(2) 
                  : '--'
        },
        {
            prop: 'paidIntBal',
            label: '当期已还利息',
            'min-width': '200',
            formatter: (record) => 
              record.paidIntBal !== null && record.paidIntBal !== undefined 
                  ? Number(record.paidIntBal).toFixed(2) 
                  : '--'
        },
        {
            prop: 'paidCoreInt',
            label: '当期已还利息(平台贴息)',
            'min-width': '200',
            formatter: (record) => 
              record.paidCoreInt !== null && record.paidCoreInt !== undefined 
                  ? Number(record.paidCoreInt).toFixed(2) 
                  : '--'
        },
        {
            prop: 'paidCustInt',
            label: '当期已还利息((客户自担))',
            'min-width': '200',
            formatter: (record) => 
              record.paidCustInt !== null && record.paidCustInt !== undefined 
                  ? Number(record.paidCustInt).toFixed(2) 
                  : '--'
        },
        {
            prop: 'repayState',
            label: '还款状态',
            'min-width': '200'
        },
    ],
  }
 },
 watch: {
   'billMonth': {
     handler() {
       // 强制更新picker选项以触发重新渲染
       this.$forceUpdate()
     },
     deep: true
   }
 },
methods: {
    // 处理开始日期变更
    handleStartDateChange(date) {
      if (date) {
        this.formModel.startBillMonth = date.getTime()
      } else {
        this.formModel.startBillMonth = ''
      }
    },
    // 处理结束日期变更
    handleEndDateChange(date) {
      if (date) {
        this.formModel.endBillMonth = date.getTime()
      } else {
        this.formModel.endBillMonth = ''
      }
    },
    handleResetForm() {
        this.$refs.searchForm.resetFields()
        this.formModel = {
            merchantId: '', // 客户编码
            merchantName: '',
            repayState:'', // 还款状态 1正常 0已结清 2逾期
            startBillMonth: '',
            endBillMonth: '',
        }
        this.billMonth = []
        this.handleGetList()
    },
    handleGetList() {
        this.$refs['dataGrid'].loadData()
    },
    handleExportFile() {
        this.fullscreenLoading = true
    },
    fetchData({pagination}) {
        const { pageSize, currentPage } = pagination;
        this.currentPage = { page: currentPage, sizes: pageSize }
        const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
        return new Promise(resolve => {
          getjdBillCreditJH(params)
            .then(response => {
              let { data,totalCount } = response
              let tableData = {
                list: data,
                pagination: {
                  pageSize: pageSize,
                  total: totalCount
                }
              }
              resolve(tableData)
            })
            .catch(() => {
              resolve({
                list: [],
                pagination: { pageSize: 10, total: 0 }
              })
            })
        })
    },
    confirmExport() {
        const exportName = '金蝶账单还款计划'
        const { sizes, page } = this.currentPage;
        const params = Object.assign({ pageSize: sizes , page},this.formModel);
        exportjdBillCreditJH(params)
        .then(response => {
            let result = response.data
            const dateStr = parseTime(new Date(), '{y}{m}{d}')
            const defExportName = `${exportName}-${dateStr}.xlsx`
            const fileExportName =
            response && response.headers && response.headers['export-filename']
                ? decodeURIComponent(response.headers['export-filename'])
                : defExportName
            download(result, fileExportName)
        })
        .catch(error => {
            console.error('导出失败:', {
            error: error,
            message: error.message,
            response: error.response,
            stack: error.stack,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
            });
            // 根据错误状态显示不同的错误信息
            const errorMsg = error.response?.data?.message 
            || error.response?.statusText 
            || error.message 
            || '导出失败，请稍后重试';
            this.$message.error(errorMsg);
        })
        .finally(() => {
            this.exportDialogVisible = false;
        })
    },
},
}
</script>

<style lang='scss' scoped>
.finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
  ::v-deep .current-row {
    background-color: #ecf5ff !important;
  }
  ::v-deep .el-table__body tr:hover > td {
    background-color: transparent !important;
  }
  ::v-deep .el-dialog {
    .el-dialog__header {
      border-bottom: 1px solid #EBEEF5;
      padding: 15px 20px;
    }
    .el-dialog__body {
      padding: 15px 20px;
    }
  }
  .export-dialog-content {
    .export-tip {
      margin-bottom: 20px;
    }
    .date-pickers {
      display: flex;
      align-items: center;
      .picker-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .picker-label {
          margin-right: 10px;
          white-space: nowrap;
        }
      }
    }
  }
</style>