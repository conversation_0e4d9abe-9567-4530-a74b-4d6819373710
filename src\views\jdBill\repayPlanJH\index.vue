<template>
    <div class="finance-stagement mt10">
        <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
            <el-form
                ref="searchForm"
                slot="search-form"
                class="statement-search-form"
                label-width="120px"
                :inline="true"
                :model="formModel"
                >
                <el-row :gutter="10">
                    <el-col :span="6">
                        <el-form-item label="客户编码" label-width="90px" prop="merchantId">
                        <el-input v-model.trim="formModel.merchantId" placeholder="请输入客户编码" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户名称" label-width="90px" prop="merchantName">
                            <el-input v-model.trim="formModel.merchantName" placeholder="请输入客户名称" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="账单月份" label-width="90px" prop="">
                            <el-date-picker
                                style="display: inline-block;width: 108.6px;"
                                v-model="billMonth[0]"
                                type="month"
                                placeholder="选择月">
                            </el-date-picker>
                            -
                            <el-date-picker
                                style="display: inline-block;width: 108.6px;"
                                v-model="endBillMonth"
                                type="month"
                                placeholder="选择月">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="还款状态" label-width="110px" prop="paidOff">
                        <el-select v-model="formModel.paidOff" placeholder="请选择还款状态" style="width:195px">
                            <el-option label="全部" value="" />
                            <el-option label="正常" :value=1 />
                            <el-option label="已结清" :value=0 />
                            <el-option label="逾期" :value=2 />
                        </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="6" :offset="17">
                        <div style="float:right;display: flex;justify-content: flex-end;">
                        <ll-button type="primary" @click="handleResetForm">重置</ll-button>
                        <ll-button type="primary" @click="handleGetList">查询</ll-button>
                        <ll-button type="primary" @click="handleExportFile">导出</ll-button>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
            <ll-data-grid
            ref="dataGrid"
            :pagination-page-size.sync="pagination.pageSize"
            :pagination-page-sizes="pagination.sizes"
            :table-columns="tableColumns"
            :async-fetch-data-fun="fetchData"
            />
        </ll-list-page-layout>
    </div>
</template>

<script>
import { getjdBillCreditJH,exportJdBillData } from '@/api/jdBill.js'
import { parseTime, download } from '@/utils/index.js'
import { start } from 'nprogress'
export default {
name:'repayPlanJH',
data() {
  return {
    fullscreenLoading: false,
    billMonth: [],
    formModel: {
        merchantId: '', // 客户编码
        merchantName: '',
        paidOff:'', // 还款状态 1正常 0已结清 2逾期
        startBillMonth: '',
        endBillMonth: '',
    },
    pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 40, 50],
    },
    tableColumns: [
        {
            prop: 'outerOrderNo',
            label: '授信编号',
            'min-width': '200'
        },
        {
            prop: 'merchantId',
            label: '客户编码',
            'min-width': '200'
        },
        {
            prop: 'merchantName',
            label: '客户名称',
            'min-width': '200'
        },
        {
            prop: 'billMonth',
            label: '账单月份',
            'min-width': '200'
        },
        {
            prop: 'freeRepayDay',
            label: '免息还款日',
            'min-width': '200'
        },
        {
            prop: 'repayDay',
            label: '应还款日',
            'min-width': '200'
        },
        {
            prop: 'billAmount',
            label: '账单待还金额',
            'min-width': '200'
        },
        {
            prop: 'principalPlan',
            label: '应还总额',
            'min-width': '200'
        },
        {
            prop: 'principalPlan',
            label: '应还本金',
            'min-width': '200'
        },
        {
            prop: 'interestPlan',
            label: '应还总利息',
            'min-width': '200'
        },
        {
            prop: 'penaltyPaid',
            label: '核企补贴应还利息',
            'min-width': '200'
        },
        {
            prop: 'penaltyPaid',
            label: '客户自担应还利息',
            'min-width': '200'
        },
        {
            prop: 'penaltyPaid',
            label: '当期已还本金',
            'min-width': '200'
        },
        {
            prop: 'penaltyPaid',
            label: '当期已还利息',
            'min-width': '200'
        },
        {
            prop: 'penaltyPaid',
            label: '当期已还利息(平台贴息)',
            'min-width': '200'
        },
        {
            prop: 'penaltyPaid',
            label: '当期已还利息((客户自担))',
            'min-width': '200'
        },
        {
            prop: 'penaltyPaid',
            label: '还款状态',
            'min-width': '200'
        },
    ],
  }
 },
methods: {
    handleResetForm() {
        this.$refs.searchForm.resetFields()
        this.formModel = {
            merchantId: '', // 客户编码
            merchantName: '',
            paidOff:'', // 还款状态 1正常 0已结清 2逾期
        }
        this.handleGetList()
    },
    handleGetList() {
        this.$refs['dataGrid'].loadData()
    },
    handleExportFile() {
        this.fullscreenLoading = true
    },
    fetchData({pagination}) {
        const { pageSize, currentPage } = pagination;
        const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
        return new Promise(resolve => {
          getjdBillCreditJH(params)
            .then(response => {
              let { data,totalCount } = response
              let tableData = {
                list: data,
                pagination: {
                  pageSize: pageSize,
                  total: totalCount
                }
              }
              resolve(tableData)
            })
            .catch(() => {
              resolve({
                list: [],
                pagination: { pageSize: 10, total: 0 }
              })
            })
        })
    },
    confirmExport() {
        if (!this.exportYear || !this.exportMonth) {
            this.$message.warning('请选择年份和月份');
            return;
        }
        // 使用选择的年月创建日期对象
        const startDate = moment(`${this.exportYear}-${this.exportMonth}-01`);
        const endDate = moment(startDate).endOf('month');
        const params = {
            payStartTime: startDate.valueOf(), // 转换为时间戳
            payEndTime: endDate.valueOf()      // 转换为时间戳
        };
        this.exportDialogVisible = true;
        // 调用原来的导出逻辑
        const exportName = '金蝶账单还款计划'
        exportJdBillData(params)
        .then(response => {
            let result = response.data
            const dateStr = parseTime(new Date(), '{y}{m}{d}')
            const defExportName = `${exportName}-${dateStr}.xlsx`
            const fileExportName =
            response && response.headers && response.headers['export-filename']
                ? decodeURIComponent(response.headers['export-filename'])
                : defExportName
            download(result, fileExportName)
        })
        .catch(error => {
            console.error('导出失败:', {
            error: error,
            message: error.message,
            response: error.response,
            stack: error.stack,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
            });
            // 根据错误状态显示不同的错误信息
            const errorMsg = error.response?.data?.message 
            || error.response?.statusText 
            || error.message 
            || '导出失败，请稍后重试';
            this.$message.error(errorMsg);
        })
        .finally(() => {
            this.exportDialogVisible = false;
        })
    },
},
}
</script>

<style lang='scss' scoped>
.finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
}
</style>