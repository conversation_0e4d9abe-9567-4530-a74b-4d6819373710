/**
 * Created by ji<PERSON><PERSON><PERSON> on 16/11/18.
 */

import xyySaasValidator from "@xyy-saas/validator";

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

/**
 * Parse the time to string
 * @param {(number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTimeMilliSecond(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  const date = new Date(time);

  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length;
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i);
    if (code > 0x7f && code <= 0x7ff) s++;
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split("?")[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, " ") +
      '"}'
  );
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += "" + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function(...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * 频率控制 返回函数连续调用时，func 执行频率限定为 次 / wait
 *
 * @param  {function}   func      传入函数
 * @param  {number}     wait      表示时间窗口的间隔
 * @param  {object}     options   如果想忽略开始边界上的调用，传入{leading: false}。
 *                                如果想忽略结尾边界上的调用，传入{trailing: false}
 * @return {function}             返回客户调用函数
 */
export function throttle(func, wait, options) {
  let context, args, result;
  let timeout = null;
  // 上次执行时间点
  let previous = 0;
  if (!options) options = {};
  // 延迟执行函数
  const later = function() {
    // 若设定了开始边界不执行选项，上次执行时间始终为0
    previous = options.leading === false ? 0 : Number(new Date());
    timeout = null;
    result = func.apply(context, args);
    if (!timeout) context = args = null;
  };
  return function(..._args) {
    const now = Number(new Date());
    // 首次执行时，如果设定了开始边界不执行选项，将上次执行时间设定为当前时间。
    if (!previous && options.leading === false) previous = now;
    // 延迟执行时间间隔
    const remaining = wait - (now - previous);
    context = this;
    args = _args;
    // 延迟时间间隔remaining小于等于0，表示上次执行至此所间隔时间已经超过一个时间窗口
    // remaining大于时间窗口wait，表示客户端系统时间被调整过
    if (remaining <= 0 || remaining > wait) {
      clearTimeout(timeout);
      timeout = null;
      previous = now;
      result = func.apply(context, args);
      if (!timeout) context = args = null;
      // 如果延迟执行不存在，且没有设定结尾边界不执行选项
    } else if (!timeout && options.trailing !== false) {
      timeout = setTimeout(later, remaining);
    }
    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  const targetObj = source && source.constructor === Array ? [] : {};
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + "";
  const randomNum = parseInt((1 + Math.random()) * 65536) + "";
  return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

/**
 * 将金额进行格式化，以万、亿为单位
 * @param opt:需要被格式化的金额、返回值的类型、小数位数等参数
 * @returns {*}
 */
export function fmtMoneyUnit(opt) {
  let result = {
    //被格式化出来的金额
    money: 0,
    //被格式化出来金额的单位：亿元、万元、元
    unit: "元"
  };

  opt = JSON.parse(
    JSON.stringify(
      Object.assign(
        {
          //需要被格式化的金额
          money: 0,
          //返回值的类型 --- obj:对象，str:字符串，默认obj
          resType: "obj",
          //保留几位小数
          n: 2,
          //是否格式化到百万级别
          showMillion: false,
          //是否格式化到千万级别
          showTenMillion: false,
          //小数点最后的0，是否保持住
          retainZero: true
        },
        opt || {}
      )
    )
  );

  opt.money = xyySaasValidator.isNull(opt.money)
    ? "0.00"
    : (opt.money + "").replace(/[^\d.-]/g, "");

  if (opt.money > 99999999 || opt.money < -99999999) {
    //如果数值是一亿以上处理数值及单位为亿元级别
    result.unit = "亿";
    // opt.money = parseInt(opt.money / Math.pow(10, (8 - opt.n))) / Math.pow(10, opt.n);
    // console.log(result.unit, '---', opt.money);
    opt.money = moneyUnit(opt.money, 8, opt.n);
  } else if (
    (opt.money > 9999999 || opt.money < -9999999) &&
    opt.showTenMillion
  ) {
    //如果数值是一万以上处理数值及单位为下元级别
    result.unit = "千万";
    // opt.money = parseInt(opt.money / Math.pow(10, (7 - opt.n))) / Math.pow(10, opt.n);
    // console.log(result.unit, '---', opt.money);
    opt.money = moneyUnit(opt.money, 7, opt.n);
  } else if ((opt.money > 999999 || opt.money < -999999) && opt.showMillion) {
    //如果数值是一万以上处理数值及单位为下元级别
    result.unit = "百万";
    // opt.money = parseInt(opt.money / Math.pow(10, (6 - opt.n))) / Math.pow(10, opt.n);
    // console.log(result.unit, '---', opt.money);
    opt.money = moneyUnit(opt.money, 6, opt.n);
  } else if (opt.money > 9999 || opt.money < -9999) {
    //如果数值是一万以上处理数值及单位为下元级别
    result.unit = "万";
    // opt.money = parseInt(opt.money / Math.pow(10, (4 - opt.n))) / Math.pow(10, opt.n);
    // console.log(result.unit, '---', opt.money);
    opt.money = moneyUnit(opt.money, 4, opt.n);
  } else {
    //如果数值是一万以下，则只处理数值即可
    // console.log(result.unit, '---', opt.money);
    // opt.money = parseInt(opt.money * Math.pow(10, opt.n)) / Math.pow(10, opt.n);
    opt.money = moneyUnit(opt.money, 0, opt.n);
  }

  //金额添加逗号进行分隔
  result.money = fmoney({
    number: opt.money,
    n: opt.n,
    retainZero: opt.retainZero
  });
  //console.log(opt.money, " --- ", result.money)

  //跟据返回类型，返回不同格式的数据
  switch (opt.resType) {
    //返回金额对象
    case "obj":
      return result;
    //返回金额字符串
    case "str":
      return result.money + result.unit;
    //默认返回金额对象
    default:
      return result;
  }
}

const moneyUnit = function(number, unitNum, n) {
  let numberStr = number + "",
    numberStrL = numberStr.split(".")[0],
    numberStrR = numberStr.split(".")[1] || "",
    hasDot = xyySaasValidator.isNull(numberStrR);

  if (numberStr.length > unitNum) {
    hasDot = true;
  }

  number = numberStrL.substring(0, numberStrL.length - unitNum);
  numberStrR = numberStrL.substring(numberStrL.length - unitNum) + numberStrR;
  if (hasDot) {
    number = number + "." + numberStrR.substring(0, n);
  }
  // console.log(numberStr, '---', numberStrL, '---', numberStrR, '---', number);

  return parseFloat(number);
};

/**
 * 数值格式化为金额
 * @param number: 需要格式化的数值
 * @param n: 保留几位小数
 * @returns {string}
 */
export function fmoney(opt) {
  var result = "";
  // console.log(opt);
  opt = JSON.parse(
    JSON.stringify(
      Object.assign(
        {
          //需要被格式化的金额
          number: "0",
          //保留几位小数
          n: 2,
          //小数点最后的0，是否保持住
          retainZero: true
        },
        opt || {}
      )
    )
  );
  //去除一些非数值的字符
  opt.number = xyySaasValidator.isNull(opt.number)
    ? "0.00"
    : (opt.number + "").replace(/[^\d.-]/g, "");
  // opt.number = (opt.number + "").replace(/[^\d\.-]/g, "");
  ////小数截取位数
  // n = n >= 0 && n <= 20 ? n : 2;
  //截取n位小数
  // opt.number = parseInt(Math.round(opt.number * Math.pow(10, opt.n))) / Math.pow(10, opt.n);
  let numberStrL = opt.number.split(".")[0];
  let numberStrR = opt.number.split(".")[1];

  // console.log(opt.number, '---', numberStrL, '---', numberStrR);
  opt.number = numberStrL;

  if (numberStrR) {
    opt.number = opt.number + "." + numberStrR.substring(0, opt.n);
  }
  opt.number = parseFloat(opt.number);

  //如果是负数,需要把负号提取出来
  if (opt.number < 0) {
    result = "-";
    opt.number = -opt.number;
  }
  //如果小数点最后的0需要保留，需要特殊处理一下
  if (opt.retainZero) {
    opt.number = opt.number.toFixed(opt.n);
  }
  //使用split,提前将Number转换成String
  opt.number = opt.number.toString();
  //整数部分(倒序，需要从个位数计算每3位加一个逗号)
  var l = opt.number
      .split(".")[0]
      .split("")
      .reverse(),
    //小数部分
    r = opt.number.split(".")[1],
    //拼接逗号后的金额字符串
    t = "";

  //倒序循环整数部分
  for (var i = 0; i < l.length; i++) {
    //每3位加一个空格（最后一个数不用加）
    t += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? "," : "");
  }
  //将拼接后的字符串反转过来，做成正常的已添加逗号的整数部分
  result += t
    .split("")
    .reverse()
    .join("");

  //如果小数部分有内容，则回调数据要加上小数
  if (!xyySaasValidator.isNull(r)) {
    result += "." + r;
  }

  return result;
}

/**
 * 将带逗号的金额字符串转为数值
 * @param moneyStr: 金额字符串
 * @returns {Number}
 */
export function rmoney(moneyStr) {
  return parseFloat(moneyStr.replace(/[^\d.-]/g, ""));
}

/**
 * 下载文件的文件流
 * 注：后端需要设置content-Type:application/actet-stream
 * 前端设置responseType:'blob'
 * @param {blob} data :文件流
 */
export function download(data, fileName) {
  console.log("下载数据不能为空")
  try {
    if (!data) {
      console.log("下载数据不能为空")
      throw new Error('下载数据不能为空');
    }
    
    const url = window.URL.createObjectURL(new Blob([data]));
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    
    // 清理创建的URL对象和DOM元素
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
  } catch (error) {
    console.error('文件下载失败:', error);
    // 如果使用了Element UI，可以显示错误提示
    if (window.ELEMENT) {
      window.ELEMENT.Message.error('文件下载失败，请稍后重试');
    }
    throw error; // 将错误抛出，让调用者可以进行处理
  }
}
