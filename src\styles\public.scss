body{
  color:#333;
}
ul,li{
  list-style: none;
}
ul{
  margin:0;padding:0;
}
dl,dd{
  margin:0;
}
.main-body{
  padding:10px 15px;
}
.hidden{
  display:none;
}
.cursor-pointer{
  cursor:pointer;
}
.align-left{
  text-align: left;
}
.align-center{
  text-align: center;
}
// font
.fb{
  font-weight: bold;
}
.f12{
  font-size: 12px;
}
.f14{
  font-size: 14px;
}
.f16{
  font-size: 16px;
}
.f18{
  font-size: 18px;
}
.f24{
  font-size: 24px;
}
.f32{
  font-size: 32px;
}
// size
.w300{
  width:300px;
}
.w400{
  width: 400px;
}
.plr10{
  padding:0 10px;
}
.h5{
  height: 5px;
}
.h10{
  height: 10px;
}
.h20{
  height: 20px;
}
// flex
.flex{
  display: flex;
}
.v-middle{
  align-items: center;
}
.v-right{
  align-items: flex-end;
}
.l-center{
  justify-content: center;
}
.l-right{
  justify-content: flex-end;
}
.l-between{
  justify-content: space-between;
}
.l-around{
  justify-content: space-around;
}
// icon
.icon-title{
  margin: 0;
  padding:16px 0 16px 16px;
  font-weight:500;
  &:before{
    content:"";
    margin-right:7px;
    width:4px;
    height:13px;
    background:rgba(31,110,255,1);
    border-radius:2px;
  }
}
// color
.bg-white{
  background:#fff
}
.c-white{
  color:#fff;
}
.c-3{
  color:#333;
}
.c-9{
  color: #999;
}