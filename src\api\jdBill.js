import request from '@/utils/request'
import requestDownload from '@/utils/requestDownload'

/**金蝶账单还款计划列表查询*/
export function getjdBillCredit(data) {
  return request({
    url: '/xydLoan/repayPlan/list',
    method: 'post',
    data
  })
}

/**金蝶账单还款计划明细导出*/
export function exportJdBillData(data) {
    return requestDownload({
      url: '/xydLoan/repayPlan/exportList',
      method: 'post',
      data
    })
}

/**金蝶还款计划明细查询*/
export function getRepayPlandDetail(data) {
    return request({
      url: '/xydLoan/repayPlan/detail',
      method: 'post',
      params:data
    })
}
/**金蝶账单还款计划(建行)列表查询*/
export function getjdBillCreditJH(data) {
    return request({
      url: '/xydLoan/repayPlan/detail',
      method: 'post',
      params:data
    })
}