<template>
  <div class="finance-stagement mt10" style="">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="交易时间" label-width="90px" prop="receiptTime">
          <el-date-picker
            v-model="formModel.receiptTime"
            type="daterange"
            :clearable="true"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="formItem[0].attrs['picker-options']"
            @change="validateFormRequiredItem"
          />
        </el-form-item>

        <el-form-item label="业务场景" label-width="90px" prop="businessOrderType">
          <ll-select v-model="formModel.businessOrderType" :options="formItem[1].attrs['options']" @change="changeBusinessOrderType"/>
        </el-form-item>

        <el-form-item label="账户类型" label-width="90px" prop="acounttype">
          <ll-select v-model="formModel.acounttype" :options="formItem[2].attrs['options']"/>
        </el-form-item>

        <el-form-item label="费用类型" label-width="90px" prop="feetype">
          <ll-search-form-select v-model="formModel.feetype" :options="formItem[3].attrs['options']" />
        </el-form-item>

        <el-form-item label="交易流水号" label-width="90px" prop="flownum">
          <el-input v-model="formModel.flownum" placeholder="交易流水号" class="samewidth" />
        </el-form-item>

        <el-form-item label="业务单号" label-width="90px" prop="businessnum">
          <el-input v-model="formModel.businessnum" placeholder="业务单号" class="samewidth" />
        </el-form-item>

        <el-form-item label="账户名称" label-width="90px" prop="acountname">
          <el-input v-model="formModel.acountname" placeholder="账户名称" class="samewidth" />
        </el-form-item>

        <el-form-item label="商户名称" label-width="90px" prop="merchantsname">
          <el-input v-model="formModel.merchantsname" placeholder="商户名称" class="samewidth" />
        </el-form-item>

        <el-form-item label="对方户名" label-width="90px" prop="othername">
          <el-input v-model="formModel.othername" placeholder="对方户名" class="samewidth" />
        </el-form-item>

        <el-form-item label="对方账号" label-width="90px" prop="otherusername">
          <el-input v-model="formModel.otherusername" placeholder="对方账号" class="samewidth" />
        </el-form-item>

        <el-form-item label="入方户名" label-width="90px" prop="accountIn">
          <el-input v-model="formModel.accountIn" placeholder="入方户名" class="samewidth" />
        </el-form-item>

        <el-form-item label="入方账号" label-width="90px" prop="accountInName">
          <el-input v-model="formModel.accountInName" placeholder="入方账号" class="samewidth" />
        </el-form-item>

        <el-form-item label=" " label-width="90px">
          <ll-button type="primary" @click="handleFormSubmit">查询</ll-button>
        </el-form-item>
      </el-form>

      <!-- 主要操作 -->
      <template slot="action-bar_left">
        <el-button @click="handleExportFile">导出Excel</el-button>
      </template>

      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :init-fetch="false"
        :async-fetch-data-fun="fetchData"
      />
    </ll-list-page-layout>
  </div>
</template>

<script>
import { queryReconcliationOrder, reportReconcliationOrder } from '@/api/report'
import { getDictMapQuery } from '@/api/common'
import moment from 'moment'
import { parseTime, download } from '@/utils/index.js'
let dataOption = []
export default {
  data() {
    return {
      searchFormKey: `search-form-key-${new Date().getTime()}`,
      formModel: {
        feetype: undefined, //费用类型
        otherusername: null, //对方账号
        othername: null, //对方户名
        accountIn: null, //我方户
        accountInName: null, //我方户名
        merchantsname: null, //商户名称
        acountname: null, //账户名称
        businessnum: null, //业务单号
        flownum: null, //交易流水号
        receiptTime: [
          moment()
            .subtract(1, 'months')
            .startOf('month')
            .startOf('hour')
            .valueOf(),
          moment()
            .subtract(1, 'months')
            .endOf('month')
            .startOf('day')
            .startOf('hour')
            .valueOf()
        ], // 交易时间
        businessOrderType: 'NULL', // 业务场景
        acounttype: 'NULL' //账户类型
      },
      formItem: [
        {
          label: '交易时间',
          prop: 'receiptTime',
          component: 'el-date-picker',
          attrs: {
            type: 'daterange',
            clearable: 'true',
            'range-separator': '至',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            'picker-options': {
              disabledDate: this.receiptTimeDisabledDate,
              onPick: this.receiptTimeOnPick
            }
          },
          width: '220px',
          error: ''
        },
        {
          label: '业务场景',
          prop: 'businessOrderType',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '账户类型',
          prop: 'acounttype',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '费用类型',
          prop: 'feetype',
          component: 'll-search-form-select',
          attrs: {
            options: []
          },
          width: '220px'
        }
      ],
      formRules: {
        receiptTime: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ],
        flownum: [
          {
            type: 'string',
            max: 60,
            message: '交易流水号长度在60字符以内',
            trigger: 'blur'
          }
        ],
        businessnum: [
          {
            type: 'string',
            max: 60,
            message: '业务单号长度在60字符以内',
            trigger: 'blur'
          }
        ],
        merchantsname: [
          {
            type: 'string',
            max: 60,
            message: '商户名称长度在60字符以内',
            trigger: 'blur'
          }
        ],
        acountname: [
          {
            type: 'string',
            max: 60,
            message: '账户名称长度在60字符以内',
            trigger: 'blur'
          }
        ],
        othername: [
          {
            type: 'string',
            max: 60,
            message: '对方户名长度在60字符以内',
            trigger: 'blur'
          }
        ],
        accountIn: [
          {
            type: 'string',
            max: 60,
            message: '入方账号长度在60字符以内',
            trigger: 'blur'
          }
        ],
        accountInName: [
          {
            type: 'string',
            max: 60,
            message: '入方账号长度在60字符以内',
            trigger: 'blur'
          }
        ]
      },
      receiptTimeOptionRange: '',
      tableColumns: [
        {
          prop: 'payNo',
          label: '交易流水号',
          width: '120'
        },
        {
          prop: 'businessType',
          label: '业务场景',
          width: '100'
        },
        {
          prop: 'accountType',
          label: '账户类型',
          width: '95'
        },
        {
          prop: 'accountName',
          label: '账户名称',
          width: '150'
        },
        {
          prop: 'dealTime',
          label: '交易时间',
          width: '150'
        },
        {
          prop: 'costType',
          label: '费用类型',
          width: '100'
        },
        {
          prop: 'amount',
          label: '金额',
          width: '100'
        },
        {
          prop: 'residual',
          label: '余额',
          width: '100'
        },
        {
          prop: 'merchantName',
          label: '商户名称',
          width: '150'
        },
        {
          prop: 'oppositeAccount',
          label: '对方账号',
          width: '150'
        },
        {
          prop: 'oppositeName',
          label: '对方户名',
          width: '150'
        },
        {
          prop: 'accountIn',
          label: '入方户名',
          width: '150'
        },
        {
          prop: 'accountInName',
          label: '入方账号',
          width: '150'
        },
        {
          prop: 'businessNo',
          label: '业务单号',
          width: '150'
        },
        {
          prop: 'remark',
          label: '备注',
          width: '150'
        }
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false,
      allAccountType:[]
    }
  },
  created() {
    // this.accountQuery();
    new Promise((resolve) => {
      getDictMapQuery("fuminBusinessTypeMap", "list").then(response => {
        this.formItem[1].attrs.options = response.result;
        resolve(response.result);
      });
    }).then(val => {
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      val.forEach(p => {
        if (dataPermission.includes(p.value)) {
          dataOption.push(p)
        }
      });
      // 附默认值
      this.formItem[2].attrs.options = dataOption[0].childrenList;
      this.formModel.acounttype = dataOption[0].childrenList[0].value;
      this.formModel.businessOrderType = dataOption[0].value;
      this.refreshList();
    })

    // costType
    getDictMapQuery("fuminCostTypeMap", "map").then(response => {
      this.formItem[3].attrs.options = response.result;
    });
  },
  mounted() {},
  methods: {
    validateFormRequiredItem() {
      this.$refs.searchForm.validateField('receiptTime')
      this.$refs.searchForm.validateField('tradeNo')
      this.$refs.searchForm.validateField('businessOrderNo')
      this.$refs.searchForm.validateField('channelTransactionNo')
    },
    /**
     * 校验 交易时间、商户订单号、业务订单号、支付渠道交易单号，至少一项填写
     */
    validateRequiredItem(rule, value, callback) {
      let errorMsg = ''
      if (this.validateParamsAllEmpty()) {
        errorMsg = '请填写任意一项筛选项'
        callback(new Error(errorMsg))
      }
      callback()
    },

    validateParamsAllEmpty() {
      const {
        // 交易时间
        receiptTime
      } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      if (this.xyySaasValidator.isNull(beginDate) && this.xyySaasValidator.isNull(endDate)) {
        return false
      }
    },
    formatterTableData(tableData, pagination, totalCount) {
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
    getFormParams(pagination) {
      const { receiptTime } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      let params = {
        payNo: this.formModel.flownum, //交易流水号
        businessType: this.formModel.businessOrderType, //业务场景
        accountType: this.formModel.acounttype, //账户类型
        costType: this.formModel.feetype, //费用类型
        businessNo: this.formModel.businessnum, //业务单号
        accountName: this.formModel.acountname, //账户名称
        merchantName: this.formModel.merchantsname, //商户名称
        oppositeName: this.formModel.othername, //对方户名
        oppositeAccount: this.formModel.otherusername, //对方账号
        accountIn: this.formModel.accountIn, //我方户名
        accountInName: this.formModel.accountInName, //我方账号
        dealStartTime: beginDate, // 交易时间 - 开始时间
        dealEndTime: endDate, // 交易时间 - 结束时间
        page: pagination ? pagination.currentPage : null,
        pageSize: pagination ? pagination.pageSize : null
      }
      delete params.receiptTime
      for (const key in params) {
        if (this.xyySaasValidator.isNull(params[key])) {
          delete params[key]
        }
      }

      return params
    },
    fetchData({ pagination }) {
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      if (dataPermission === '') {
        let obj = {
          list: [],
          pagination: { total: 0 }
        }
        return Promise.resolve(obj)
      }
      let params = this.getFormParams(pagination)
      return new Promise(resolve => {
        queryReconcliationOrder(params)
          .then(response => {
            let { data } = response
            let { totalCount } = response
            let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },

    receiptTimeDisabledDate(time) {
      // console.log("receiptTimeDisabledDate", moment(time));
      let receiptTimeOptionRange = this.receiptTimeOptionRange

      if (receiptTimeOptionRange) {
        // let secondNum = 60 * 60 * 24 * 3 * 1000;
        let startTime = receiptTimeOptionRange,
          currTime = moment(time),
          minTime = moment(startTime).subtract(1, 'months'),
          maxTime = moment(startTime).add(1, 'months')
        return currTime.isBefore(minTime) || currTime.isAfter(maxTime)
      } else {
        return false
      }
    },
    receiptTimeOnPick(time) {
      //当第一时间选中才设置禁用
      if (time.minDate && !time.maxDate) {
        this.receiptTimeOptionRange = time.minDate
      }
      if (time.maxDate) {
        this.receiptTimeOptionRange = null
      }
    },

    // 业务类型值变化
    changeBusinessOrderType(type) {
      dataOption.forEach(option => {
        console.log(option);
        if(option.value == type) {
          // 附默认值
          this.formItem[2].attrs.options = option.childrenList;
          this.formModel.acounttype = option.childrenList[0].value;
        }
      });
    },

    // 重新加载表
    refreshList() {
      this.$refs['dataGrid'].loadData()
    },

    // 查询
    handleFormSubmit() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.refreshList()
        } else {
          return false
        }
      })
    },

    // 导出excel
    handleExportFile() {
      this.fullscreenLoading = true
      let exportParams = this.getFormParams()
      const exportName = '富民子账户对账'
      reportReconcliationOrder(exportParams)
        .then(response => {
          let result = response.data
          const dateStr = parseTime(new Date(), '{y}{m}{d}')
          const defExportName = `${exportName}-${dateStr}.xlsx`
          const fileExportName =
            response && response.headers && response.headers['export-filename']
              ? decodeURIComponent(response.headers['export-filename'])
              : defExportName
          download(result, fileExportName)
        })
        .finally(() => {
          this.fullscreenLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
</style>
