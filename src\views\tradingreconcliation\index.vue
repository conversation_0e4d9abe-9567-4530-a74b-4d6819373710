<template>
  <div class="finance-stagement mt10" style="">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="交易时间" label-width="90px" prop="receiptTime">
          <el-date-picker
            v-model="formModel.receiptTime"
            type="daterange"
            :clearable="true"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="formItem[0].attrs['picker-options']"
            @change="validateFormRequiredItem"
          />
        </el-form-item>

        <el-form-item label="业务场景" label-width="90px" prop="businessOrderType">
          <ll-select v-model="formModel.businessOrderType" :options="formItem[1].attrs['options']" />
        </el-form-item>

        <el-form-item label="账户类型" label-width="90px" prop="acounttype">
          <ll-select v-model="formModel.acounttype" :options="formItem[2].attrs['options']" />
        </el-form-item>

        <el-form-item label=" " label-width="90px">
          <ll-button type="primary" @click="handleFormSubmit">查询</ll-button>
        </el-form-item>
      </el-form>

      <!-- 主要操作 -->
      <template slot="action-bar_left">
        <el-button @click="handleExportFile">导出Excel</el-button>
        <el-upload
          style="display: inline; margin: 0 20px"
          class="upload-demo"
          :action="fakeaction"
          :show-file-list="false"
          :http-request="reportPayRecordMap">
          <el-button size="small" type="primary">点击上传</el-button>
        </el-upload>
      </template>


      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
      />
    </ll-list-page-layout>
  </div>
</template>

<script>
import { tradeCheck, reportTradeCheck, reportPayRecordMapExport } from '@/api/report'
import moment from 'moment'
import { parseTime, download } from '@/utils/index.js'
const dataOption = [
  { desc: '宜块钱', label: '宜块钱', value: 'YKQ' },
  { desc: 'POP', label: 'POP', value: 'POP' },
  { desc: '智慧脸商城', label: '智慧脸商城', value: 'ZHL' },
  { desc: '药帮忙', label: '药帮忙', value: 'ec' }
]
export default {
  data() {
    return {
      typeindex: 0, //费用类型的下标
      searchFormKey: `search-form-key-${new Date().getTime()}`,
      formModel: {
        receiptTime: [
          moment()
            .subtract(1, 'months')
            .startOf('month')
            .startOf('hour')
            .valueOf(),
          moment()
            .subtract(1, 'months')
            .endOf('month')
            .startOf('day')
            .startOf('hour')
            .valueOf()
        ], // 交易时间
        businessOrderType: 'ec', // 业务场景
        acounttype: '' //账户类型
      },
      formItem: [
        {
          label: '交易时间',
          prop: 'receiptTime',
          component: 'el-date-picker',
          attrs: {
            type: 'daterange',
            clearable: 'true',
            'range-separator': '至',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            'picker-options': {
              disabledDate: this.receiptTimeDisabledDate,
              onPick: this.receiptTimeOnPick
            }
          },
          width: '220px',
          error: ''
        },
        {
          label: '业务场景',
          prop: 'businessOrderType',
          component: 'll-select',
          attrs: {
            options: [
              { desc: '药帮忙', label: '药帮忙', value: 'ec' },
              { desc: '荷叶健康', label: '荷叶健康', value: 'yikuaiqian' }
            ]
          },
          width: '220px'
        },
        {
          label: '平台账户',
          prop: 'acounttype',
          component: 'll-select',
          attrs: {
            options: [
              { desc: '全部', label: '全部', value: '' },
              { desc: '武汉小药药', label: '武汉小药药', value: 'ec_self_support_mini_XS420000' },
              { desc: '杭州小药药', label: '杭州小药药', value: 'ec_self_support_mini_XS330000' },
              { desc: '厦门小药药', label: '厦门小药药', value: 'ec_self_support_mini_XS350000' },
              { desc: '山东小药药', label: '山东小药药', value: 'ec_self_support_mini_XS370000' },
              { desc: '云南小药药', label: '云南小药药', value: 'ec_self_support_mini_XS530000' },
              { desc: '长沙小药药', label: '长沙小药药', value: 'ec_self_support_mini_XS430000' },
              { desc: '重庆小药药', label: '重庆小药药', value: 'ec_self_support_mini_XS500000' },
              { desc: '郑州小药药', label: '郑州小药药', value: 'ec_self_support_mini_XS410000' },
              { desc: '南昌小药药', label: '南昌小药药', value: 'ec_self_support_mini_XS360000' },
              { desc: '山西小药药', label: '山西小药药', value: 'ec_self_support_mini_XS140001' },
              { desc: '荷叶轮训', label: '荷叶轮训', value: 'yikuaiqian_fm' },
              { desc: '荷叶国控', label: '荷叶国控', value: 'yikuaiqian_doc_gk' },
              { desc: '荷叶微信（1602773490）', label: '荷叶微信（1602773490）', value: '1602773490' },
              { desc: '荷叶微信（1611876422）', label: '荷叶微信（1611876422）', value: '1611876422' },
              { desc: '药帮忙POP', label: '平安退款垫资及回补子账户', value: '平安退款垫资及回补子账户' },
              { desc: '药帮忙POP', label: '平安退佣金子账户', value: '平安退佣金子账户' },
              { desc: '药帮忙POP', label: '平安购物金子账户', value: '平安购物金子账户' },
              { desc: '药帮忙POP', label: '平安营销子账户', value: '平安营销子账户' },
              { desc: '药帮忙POP', label: '平安收佣金、退营销、退购物金、富民退款垫资回补子账户', value: '平安收佣金、退营销、退购物金、富民退款垫资回补子账户' },
              { desc: '药帮忙POP', label: '平安挂账子账户（池子）', value: '平安挂账子账户（池子）' },
              { desc: '药帮忙POP', label: '平安POP未开基本户大商户', value: '平安POP未开基本户大商户' },
              { desc: '药帮忙POP', label: '平安POP商业子账户', value: '平安POP商业子账户' },
              { desc: '药帮忙POP', label: '【平安内部】退款垫款子账户', value: '【平安内部】退款垫款子账户' },
              { desc: '药帮忙POP', label: '【平安内部】营销子账户', value: '【平安内部】营销子账户' },
              { desc: '药帮忙POP', label: '【平安内部】未知子账户', value: '【平安内部】未知子账户' },
              { desc: '药帮忙POP', label: '【平安内部】平台担保子账户', value: '【平安内部】平台担保子账户' },
              { desc: '药帮忙POP', label: '【平安内部】平台在途子账户', value: '【平安内部】平台在途子账户' },
              { desc: '药帮忙POP', label: '【平安内部】利息子账户', value: '【平安内部】利息子账户' },
              { desc: '药帮忙POP', label: '【平安内部】云收款T1专属子账户', value: '【平安内部】云收款T1专属子账户' }
            ]
          },
          width: '220px'
        }
      ],
      formRules: {
        receiptTime: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ]
      },
      receiptTimeOptionRange: '',
      tableColumns: [
        {
          prop: 'businessTypeName',
          label: '业务场景',
          width: '120'
        },
        {
          prop: 'businessOrderTypeName',
          label: '账户类型',
          width: '150'
        },
        {
          prop: 'merchantNo',
          label: '商户号',
          width: '120'
        },
        {
          prop: 'clientSerialNo',
          label: '请求流水号',
          width: '100'
        },
        {
          prop: 'orderNo',
          label: '交易流水号',
          width: '95'
        },
        {
          prop: 'amount',
          label: '交易金额',
          width: '150'
        },
        {
          prop: 'tradeStatus',
          label: '交易状态',
          width: '150'
        },
        {
          prop: 'tradeType',
          label: '交易类型',
          width: '100'
        },
        {
          prop: 'payTime',
          label: '交易订单创建时间',
          width: '150'
        },
        {
          prop: 'successTime',
          label: '交易订单完成时间',
          width: '150'
        },
        {
          prop: 'outAccountNo',
          label: '出账方账号',
          width: '150'
        },
        {
          prop: 'outAccountName',
          label: '出账方户名',
          width: '150'
        },
        {
          prop: 'outBankCode',
          label: '出账方银行简称',
          width: '150'
        },
        {
          prop: 'inAccountNo',
          label: '入账方账号',
          width: '150'
        },
        {
          prop: 'inAccountName',
          label: '入账方户名',
          width: '150'
        },
        {
          prop: 'inBankCode',
          label: '入账方银行简称',
          width: '150'
        },
        {
          prop: 'inCardNo',
          label: '入账方证件号',
          width: '150'
        },
        {
          prop: 'extra',
          label: '商户保留域',
          width: '150'
        },
        {
          prop: 'propateInfo',
          label: '分账信息',
          width: '150'
        },
        {
          prop: 'furunInfo',
          label: '分润信息',
          width: '150'
        },
        {
          prop: 'remarks',
          label: '交易备注',
          width: '150'
        },
        {
          prop: 'feeAmount',
          label: '手续费',
          width: '150'
        },
        {
          prop: 'guarantee',
          label: '担保信息',
          width: '150'
        },
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false,
      uploadExcelUrl: "",
    }
  },
  created() {
    this.accountQuery();
    this.uploadExcelUrl = process.env.VUE_APP_URL + "/queryTrade/download";
  },
  mounted() {},
  methods: {
    // 获取用户信息
    accountQuery() {
      let option = []
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      dataOption.forEach(p => {
        if (dataPermission.includes(p.value)) {
          option.push(p)
        }
      })
      // if (dataPermission.includes('YKQ')) {
      //   this.formModel.businessOrderType = 'YKQ'
      // }
      // this.formItem[1].attrs.options = option
      // if (option.length === 1) {
      //   this.formModel.businessOrderType = option[0].value
      // }
    },
    validateFormRequiredItem() {
      this.$refs.searchForm.validateField('receiptTime')
      this.$refs.searchForm.validateField('tradeNo')
      this.$refs.searchForm.validateField('businessOrderNo')
      this.$refs.searchForm.validateField('channelTransactionNo')
    },
    /**
     * 校验 交易时间、商户订单号、业务订单号、支付渠道交易单号，至少一项填写
     */
    validateRequiredItem(rule, value, callback) {
      let errorMsg = ''
      if (this.validateParamsAllEmpty()) {
        errorMsg = '请填写任意一项筛选项'
        callback(new Error(errorMsg))
      }
      callback()
    },

    validateParamsAllEmpty() {
      const {
        // 交易时间
        receiptTime
      } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      if (this.xyySaasValidator.isNull(beginDate) && this.xyySaasValidator.isNull(endDate)) {
        return false
      }
    },
    formatterTableData(tableData, pagination, totalCount) {
      console.log(tableData)
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
    getFormParams(pagination) {
      const { receiptTime } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      let params = {
        businessType: this.formModel.businessOrderType, //业务场景
        businessOrderType: this.formModel.acounttype, //账户类型
        payStartTime: beginDate, // 交易时间 - 开始时间
        payEndTime: endDate, // 交易时间 - 结束时间
        page: pagination ? pagination.currentPage : null,
        pageSize: pagination ? pagination.pageSize : null
      }
      delete params.receiptTime
      for (const key in params) {
        if (this.xyySaasValidator.isNull(params[key])) {
          delete params[key]
        }
      }

      return params
    },
    fetchData({ pagination }) {
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      if (dataPermission === '') {
        let obj = {
          list: [],
          pagination: { total: 0 }
        }
        return Promise.resolve(obj)
      }
      let params = this.getFormParams(pagination)
      return new Promise(resolve => {
        tradeCheck(params)
          .then(response => {
            let { data } = response
            let { totalCount } = response
            let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },

    receiptTimeDisabledDate(time) {
      // console.log("receiptTimeDisabledDate", moment(time));
      let receiptTimeOptionRange = this.receiptTimeOptionRange

      if (receiptTimeOptionRange) {
        // let secondNum = 60 * 60 * 24 * 3 * 1000;
        let startTime = receiptTimeOptionRange,
          currTime = moment(time),
          minTime = moment(startTime).subtract(1, 'months'),
          maxTime = moment(startTime).add(1, 'months')
        return currTime.isBefore(minTime) || currTime.isAfter(maxTime)
      } else {
        return false
      }
    },
    receiptTimeOnPick(time) {
      //当第一时间选中才设置禁用
      if (time.minDate && !time.maxDate) {
        this.receiptTimeOptionRange = time.minDate
      }
      if (time.maxDate) {
        this.receiptTimeOptionRange = null
      }
    },

    // 重新加载表
    refreshList() {
      this.$refs['dataGrid'].loadData()
    },

    // 查询
    handleFormSubmit() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.refreshList()
        } else {
          return false
        }
      })
    },

    // 导出excel
    handleExportFile() {
      this.fullscreenLoading = true
      let exportParams = this.getFormParams()
      const exportName = '富民交易对账'
      reportTradeCheck(exportParams)
        .then(response => {
          let result = response.data
          const dateStr = parseTime(new Date(), '{y}{m}{d}')
          const defExportName = `${exportName}-${dateStr}.csv`
          const fileExportName =
            response && response.headers && response.headers['export-filename']
              ? decodeURIComponent(response.headers['export-filename'])
              : defExportName
          download(result, fileExportName)
        })
        .finally(() => {
          this.fullscreenLoading = false
        })
    },
    reportPayRecordMap(params){
      const file = params.file;
      // 根据后台需求数据格式
      const form = new FormData();
      // 文件对象
      form.append("file", file);
      // 本例子主要要在请求时添加特定属性，所以要用自己方法覆盖默认的action
      form.append("clientType", 'multipart/form-data');
      // 项目封装的请求方法，下面做简单介绍
      reportPayRecordMapExport(form)
        .then(res => {
          var blob = new Blob([res.data],{ type: "application/vnd.ms-excel" }); // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
          var downloadElement = document.createElement('a');
          var href = window.URL.createObjectURL(blob); // 创建下载的链接
          downloadElement.href = href;
          downloadElement.download = '支付流水映射文件.xlsx'; // 下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); // 点击下载
          document.body.removeChild(downloadElement); // 下载完成移除元素
          window.URL.revokeObjectURL(href); // 释放掉blob对象
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
</style>
