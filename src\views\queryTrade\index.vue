<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="富民交易查询" name="fuminRecord" style="height: 630px">
      <div class="finance-stagement mt10">
        <ll-list-page-layout>
          <el-form
            ref="searchFuminRecordForm"
            slot="search-form"
            class="statement-search-form"
            label-width="120px"
            :inline="true"
            :model="formModel"
            :rules="formRules"
          >
            <el-form-item label="交易时间" label-width="90px" prop="receiptTime">
              <el-date-picker
                v-model="formModel.receiptTime"
                type="daterange"
                :clearable="true"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="formItem[0].attrs['picker-options']"
                @change="validateFormRequiredItem"
              />
            </el-form-item>

              <el-form-item label="平台账号ID" label-width="100px" prop="accountId">
                <el-input v-model="formModel.accountId" placeholder="平台账号ID" class="samewidth" />
              </el-form-item>

              <el-form-item label=" " label-width="90px">
                <ll-button type="primary" @click="handleFuminRecordFormSubmit">查询</ll-button>
              </el-form-item>
            </el-form>

          <ll-data-grid
            ref="dataGrid"
            :pagination-page-size.sync="fuminRecordPagination.pageSize"
            :pagination-page-sizes="fuminRecordPagination.sizes"
            :table-columns="tableColumns"
            :async-fetch-data-fun="fetchData"
            :init-fetch="false"
          />
        </ll-list-page-layout>
      </div>
    </el-tab-pane>
    <el-tab-pane label="富民账单状态查询" name="fuminTradeStatus">
      <div class="finance-stagement mt10">
        <ll-list-page-layout>
          <el-form  ref="transactionFromRef" :inline="true" :model="transactionFrom" :rules="formRules" class="demo-form-inline">
            <el-form-item label="订单号">
              <el-input v-model="transactionFrom.tradeNo" placeholder="平台订单号" />
            </el-form-item>
            <el-form-item label="交易类型" v-if="show.transactionType">
              <ll-select v-model="transactionFrom.transactionType" :options="transactionType" @change="transactionTypeChange" />
            </el-form-item>
            <el-form-item label="支付渠道" v-if="show.channel">
              <ll-select v-model="transactionFrom.channel" :options="payChannelType" />
            </el-form-item>
            <el-form-item label="支付模式" v-if="show.payMode">
              <ll-select v-model="transactionFrom.payMode" :options="payModeType" />
            </el-form-item>
            <el-form-item label="订单业务线" v-if="show.businessOrderType">
              <el-input v-model="transactionFrom.businessOrderType" placeholder="订单业务线" />
            </el-form-item>
            <el-form-item label="查询本地">
              <el-switch
                v-model="transactionFrom.queryLocalData"
                active-color="#13ce66"
                inactive-color="#ff4949" />
            </el-form-item>
            <el-form-item label="是否回调">
              <el-switch v-model="transactionFrom.callback" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="transactionFromSubmit" :loading="transactionSearchLoading">查询</el-button>
            </el-form-item>
          </el-form>

          <el-divider content-position="left">查询结果</el-divider>

          <el-form :inline="true" :model="transactionResultInfo" class="demo-form-inline" style="width: 300px">
            <el-form-item label="交易单号">
              <el-input v-model="transactionResultInfo.tradeNo" disabled />
            </el-form-item>
            <el-form-item label="交易金额">
              <el-input v-model="transactionResultInfo.amount" disabled />
            </el-form-item>
            <el-form-item label="交易状态">
              <el-tag v-show="transactionResultInfo.status === '0'"> 处理中 </el-tag>
              <el-tag type="success" v-show="transactionResultInfo.status === '1'"> 成功 </el-tag>
              <el-tag type="danger" v-show="transactionResultInfo.status === '2'"> 失败 </el-tag>
              <el-tag type="info" v-show="transactionResultInfo.status === '3'"> 无此交易 </el-tag>
              <el-tag type="danger" v-show="transactionResultInfo.status === '5'"> 交易关闭 </el-tag>
              <el-tag type="danger" v-show="transactionResultInfo.status === '15'"> 退汇 </el-tag>
            </el-form-item>
            <el-form-item label="交易时间">
              <el-input v-model="transactionResultInfo.successTime" disabled />
            </el-form-item>
            <el-form-item label="业务单号">
              <el-input v-model="transactionResultInfo.businessOrderNo" disabled />
            </el-form-item>
            <el-form-item label="渠道流水">
              <el-input v-model="transactionResultInfo.channelChargeNo" disabled/>
            </el-form-item>
            <el-form-item label="收款商户">
              <el-input v-model="transactionResultInfo.merchantId" disabled />
            </el-form-item>
          </el-form>
        </ll-list-page-layout>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import moment from "moment";
import { queryFuminRecord, queryTransactionInfo } from "@/api/report";
import { getDictMapQuery } from "@/api/common";

export default {
  data() {
    return {
      activeName: 'fuminRecord',
      searchFormKey: `search-form-key-${new Date().getTime()}`,
      formModel: {
        receiptTime: [
          moment()
            .subtract(1, 'months')
            .startOf('month')
            .startOf('hour')
            .valueOf(),
          moment()
            .subtract(1, 'months')
            .endOf('month')
            .startOf('day')
            .startOf('hour')
            .valueOf()
        ], // 交易时间
        accountId: "",
      },
      formItem: [
        {
          label: '交易时间',
          prop: 'receiptTime',
          component: 'el-date-picker',
          attrs: {
            type: 'daterange',
            clearable: 'true',
            'range-separator': '至',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            'picker-options': {
              disabledDate: this.receiptTimeDisabledDate,
              onPick: this.receiptTimeOnPick
            }
          },
          width: '220px',
          error: ''
        },
      ],
      formRules: {
        receiptTime: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ]
      },
      receiptTimeOptionRange: '',
      tableColumns: [
        {
          prop: 'accountIn',
          label: 'accountIn',
          'min-width': '120'
        },
        {
          prop: 'accountInName',
          label: 'accountInName',
          'min-width': '120'
        },
        {
          prop: 'accountNo',
          label: 'accountNo',
          'min-width': '120'
        },
        {
          prop: 'accountOut',
          label: 'accountOut',
          'min-width': '120'
        },
        {
          prop: 'accountOutName',
          label: 'accountOutName',
          'min-width': '120'
        },
        {
          prop: 'amount',
          label: 'amount',
          'min-width': '120'
        },
        {
          prop: 'balance',
          label: 'balance',
          'min-width': '120'
        },
        {
          prop: 'direction',
          label: 'direction',
          'min-width': '120'
        },
        {
          prop: 'flowNo',
          label: 'flowNo',
          'min-width': '120'
        },
        {
          prop: 'handStatus',
          label: 'handStatus',
          'min-width': '120'
        },
        {
          prop: 'memo',
          label: 'memo',
          'min-width': '120'
        },
        {
          prop: 'serialNumber',
          label: 'serialNumber',
          'min-width': '120'
        },
        {
          prop: 'tradeDate',
          label: 'tradeDate',
          'min-width': '120'
        },
        {
          prop: 'tradeFlowNo',
          label: 'tradeFlowNo',
          'min-width': '120'
        },
        {
          prop: 'tradeType',
          label: 'tradeType',
          'min-width': '120'
        },
      ],
      // 交易查询
      transactionFrom: {
        tradeNo: "",
        transactionType: "pay",
        channel: "",
        payMode: "",
        businessOrderType: "",
        queryLocalData: true,
        callback: false
      },
      payChannelType: [],
      payModeType: [],
      transactionType: [],
      transactionStatusType: [],
      transactionResultInfo: {
        tradeNo:"",
        amount:"",
        successTime:"",
        status:"",
        merchantId:"",
        channelChargeNo:"",
        channel:"",
        businessOrderNo:"",
      },
      fuminRecordPagination: {
        pageSize: 20,
        sizes: [20]
      },
      show: {
        transactionType: true,
        channel: true,
        payMode: true,
        businessOrderType: false,
      },
      transactionSearchLoading: false,
    };
  },
  created() {
    getDictMapQuery("payChannelTypeMap", "map").then(response => {
      this.payChannelType = response.result;
    })
    getDictMapQuery("payModeTypeMap", "map").then(response => {
      this.payModeType = response.result;
    })
    getDictMapQuery("transactionTypeMap", "map").then(response => {
      this.transactionType = response.result;
    })
    getDictMapQuery("transactionStatusTypeMap", "map").then(response => {
      this.transactionStatusType = response.result;
    })
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    validateFormRequiredItem() {
      this.$refs.searchFuminRecordForm.validateField('receiptTime')
      this.$refs.searchFuminRecordForm.validateField('accountId')
    },
    /**
     * 校验 交易时间、商户订单号、业务订单号、支付渠道交易单号，至少一项填写
     */
    validateRequiredItem(rule, value, callback) {
      let errorMsg = ''
      if (this.validateParamsAllEmpty()) {
        errorMsg = '请填写任意一项筛选项'
        callback(new Error(errorMsg))
      }
      callback()
    },

    validateParamsAllEmpty() {
      const {
        // 交易时间
        receiptTime
      } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      if (this.xyySaasValidator.isNull(beginDate) && this.xyySaasValidator.isNull(endDate)) {
        return false
      }
    },
    // 查询
    handleFuminRecordFormSubmit() {
      this.$refs.searchFuminRecordForm.validate(valid => {
        if (valid) {
          this.refreshList()
        } else {
          return false
        }
      })
    },
    // 重新加载表
    refreshList() {
      this.$refs['dataGrid'].loadData()
    },
    getFormParams(pagination) {
      const { receiptTime } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      let params = {
        accountId: this.formModel.accountId, //账号
        beginTime: beginDate, // 交易时间 - 开始时间
        endTime: endDate, // 交易时间 - 结束时间
        pageNo: pagination ? pagination.currentPage : null
      }
      return params
    },
    fetchData({ pagination }) {
      let params = this.getFormParams(pagination)
      let a = new Promise(resolve => {
        queryFuminRecord(params)
          .then(response => {
            console.log(response);
            let { data } = response
            let { totalCount } = response
            let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch(() => {
            console.log("abc")
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
      console.log(a);
      return a;
    },
    formatterTableData(tableData, pagination, totalCount) {
      console.log(tableData)
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      console.log(reportQueryOrderViewBos);
      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
    transactionFromSubmit(){
      this.$refs.transactionFromRef.validate(valid => {
        if (valid) {
          this.transactionSearchLoading = true;
          queryTransactionInfo(this.transactionFrom).then(response => {
            this.transactionResultInfo = response.result;
            this.transactionResultInfo.successTime = moment(this.transactionResultInfo.successTime).format('YYYY-MM-DD HH:mm:ss');
            this.transactionResultInfo.amount = this.transactionResultInfo.amount / 100;
          }).finally(() => {
            this.transactionSearchLoading = false;
          })
        } else {
          return false
        }
      })
    },
    transactionTypeChange(data){
      if("pay" === data) {
        this.show.transactionType = true;
        this.show.channel = true;
        this.show.payMode = true;
        this.show.businessOrderType = false;

        this.transactionFrom.businessOrderType="";
        this.transactionFrom.payMode="";
        this.transactionFrom.channel="";
      }
      if("refund" === data) {
        this.show.transactionType = true;
        this.show.channel = true;
        this.show.payMode = false;
        this.show.businessOrderType = false;

        this.transactionFrom.businessOrderType="";
        this.transactionFrom.payMode="";
        this.transactionFrom.channel="";
      }
      if("withdraw" === data) {
        this.show.transactionType = true;
        this.show.channel = false;
        this.show.payMode = false;
        this.show.businessOrderType = true;

        this.transactionFrom.businessOrderType="";
        this.transactionFrom.payMode="";
        this.transactionFrom.channel="";
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
</style>
