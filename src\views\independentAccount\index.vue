<template>
    <div class="finance-stagement mt10">
      <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
        <el-form
          ref="searchForm"
          slot="search-form"
          class="statement-search-form"
          label-width="120px"
          :inline="true"
          :model="formModel"
          >
          <!-- :rules="formRules" -->
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="商户编码" label-width="90px" prop="receiverName">
              <el-input v-model.trim="formModel.receiverName" placeholder="请输入商户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商户名称" label-width="90px" prop="payerName">
              <el-input v-model.trim="formModel.payerName" placeholder="请输入客户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称" label-width="90px" prop="businessPayNo">
              <el-input v-model.trim="formModel.businessPayNo" placeholder="请输入平台订单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="药帮忙订单号" label-width="90px" prop="businessPayNo">
              <el-input v-model.trim="formModel.businessPayNo" placeholder="请输入平台订单号" class="samewidth" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col :span="6">
                <el-form-item label="支付单号" label-width="90px" prop="businessPayNo">
                <el-input v-model.trim="formModel.businessPayNo" placeholder="请输入平台订单号" class="samewidth" />
                </el-form-item>
            </el-col>
            <!-- 订单类型：默认全部，可筛选支付单、退款单 -->
          <el-col :span="6">
            <el-form-item label="订单类型" label-width="90px" prop="loanBalanceNo">
                <el-select v-model="formModel.payoffflag" placeholder="请选择订单类型">
                    <el-option label="全部" :value="0" />
                    <el-option label="支付单" :value="1" />
                    <el-option label="退款单" :value="2" />
                </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="清算状态" label-width="90px" prop="payoffflag">
              <el-select v-model="formModel.payoffflag" placeholder="请选择清算状态">
                <el-option label="全部" :value="0" />
                <el-option label="已清算" :value="1" />
                <el-option label="未清算" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="处理时间" label-width="90px">
                <el-date-picker
                    v-model="payDay"
                    type="daterange"
                    format="yyyy-MM-dd"
                    value-format="timestamp"
                    :range-separator="'至'"
                    :clearable="true"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="validateFormRequiredItem"
                    >
                </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div>
              <ll-button @click="handleExportFile">导出Excel</ll-button>
              <ll-button @click="handleResetForm">重置</ll-button>
              <ll-button type="primary" @click="handleGetList">查询</ll-button>
            </div>
          </el-col>
        </el-row>
        </el-form>
        <ll-data-grid
          ref="dataGrid"
          :pagination-page-size.sync="pagination.pageSize"
          :pagination-page-sizes="pagination.sizes"
          :table-columns="tableColumns"
          :async-fetch-data-fun="fetchData"
        >
        <template #payDay="{record}">
          <span>{{ (record.payDay) ? `${moment(record.payDay).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #curRepayDate="{record}">
          <span>{{ (record.curRepayDate) ? `${moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #actRepayDate="{record}">
          <span>{{ (record.actRepayDate) ? `${moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #payoffflag="{record}">
          <span>{{ {1:'已结清',0:'未结清'}[record.payoffflag] }}</span>
        </template>
        </ll-data-grid>
      </ll-list-page-layout>
    </div>
  </template>
  
  <script>
  import {getProjectList} from '@/api/openAccount.js'
  import qs from 'qs';
  // 引入处理时间戳
  import moment from 'moment'
  export default {
    data() {
      return {
        qs,
        payDay:[],
        actRepayDate:[], //实际还款日
        curRepayDate:[], //应还款日
        formModel: {
            receiverName: '', // 商户名称
            payerName: '', //客户名称
            // 支付日期
            startPayDate:'',
            endPayDate:'',
            // 应还日
            startCurRepayDate:'',
            endCurRepayDate:'',
            // 实还日
            startActRepayDate:'',
            endActRepayDate:'',
            businessPayNo:'', //平台订单号
            loanBalanceNo:'', //借据号
            payoffflag:'' //还款状态
        },
        tableColumns: [
          {
            prop: 'receiverName',
            label: '商户编码',
            'min-width': '120'
          },
          {
            prop: 'payerName',
            label: '商户名称',
            'min-width': '200'
          },
          {
            prop: 'businessPayNo',
            label: '客户名称',
            'min-width': '200'
          },
          {
            prop: 'payDay',
            label: '药帮忙订单号',
            'min-width': '200',
            formatter:(record) =>
            record.payDay ? moment(record.payDay).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'loanBalanceNo',
            label: '订单号',
            'min-width': '200'
          },
          {
            prop: 'curTerm',
            label: '银行订单号',
            'min-width': '95'
          },
          {
            prop: 'curNum',
            label: '订单金额',
            'min-width': '95'
          },
          {
            prop: 'curRepayDate',
            label: '业务类型',
            'min-width': '95',
            formatter:(record) =>
            record.curRepayDate ? moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'actRepayDate',
            label: '订单类型',
            'min-width': '95',
            formatter:(record) =>
            record.actRepayDate ? moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'payoffflag',
            label: '订单状态',
            'min-width': '95',
            formatter:(record) =>{
              return {1:'已结清',0:'未结清'}[record.payoffflag]
            }
          },
          {
            prop: 'curRepayAmount',
            label: '已退款金额',
            'min-width': '95'
          },
          {
            prop: 'actRepayAmount',
            label: '清算状态',
            'min-width': '95'
          },
          {
            prop: 'curPrincipalAmount',
            label: '交易时间',
            'min-width': '95'
          },
          {
            prop: 'actPrincipalAmount',
            label: '处理时间',
            'min-width': '95'
          },
          {
            prop: 'curInterestAmount',
            label: '原订单号',
            'min-width': '95'
          },
          {
            prop: 'curSubsidyInterestAmount',
            label: '手续费',
            'min-width': '95'
          },
          {
            prop: 'actInterestAmount',
            label: '账户名称',
            'min-width': '95'
          },
          {
            prop: 'actSubsidyInterestAmount',
            label: '银行名称',
            'min-width': '95'
          },
          {
            prop: 'curCompoundInterestAmount',
            label: '业务订单号',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '交易号',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '平台商户订单号',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '平台商户号',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '平台商户名称',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '平台在途户',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '子商户号',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '佣金信息',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '营销信息',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '佣金总金额',
            'min-width': '95'
          },
          {
            prop: 'actCompoundInterestAmount',
            label: '备注',
            'min-width': '95'
          },
        ],
        pagination: {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        fullscreenLoading: false,
      }
    },
    created(){
      this.payDay = [this.lastMonthFirstDay(),new Date().setDate(new Date().getDate() - 1)]
      this.formModel.startPayDate = this.payDay[0]
      this.formModel.endPayDate = this.payDay[1]
    },
    computed: {
    },
    mounted() {},
    methods: {
      lastMonthFirstDay(){
        const date = new Date()
        date.setMonth(date.getMonth() -1);//获取上一个月
        date.setDate(1);//设置为1号
        return date.getTime();
      },
      // 实际还款日
      handlePickerPractical(val){
        if (val) {
          this.formModel.startActRepayDate = val[0]
          this.formModel.endActRepayDate = val[1]
        } else {
          this.formModel.startActRepayDate = ''
          this.formModel.endActRepayDate = ''
        }
      },
      // 应还款日
      handlePickerDueDate(val){
        console.log(val);
        if (val) {
          this.formModel.startCurRepayDate = val[0]
          this.formModel.endCurRepayDate = val[1]
        } else {
          this.formModel.startCurRepayDate = ''
          this.formModel.endCurRepayDate = ''
        }
      },
      // 订单支付时间
      validateFormRequiredItem(val) {
        if (val) {
          this.formModel.startPayDate = val[0]
          this.formModel.endPayDate = val[1]
        } else {
          this.formModel.startPayDate = ''
          this.formModel.endPayDate = ''
        }
      },
      handleGetList() {
        this.$refs['dataGrid'].loadData()
      },
      fetchData({pagination}) {
        console.log(pagination);
        const { pageSize, currentPage } = pagination;
        const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
        return new Promise(resolve => {
          getProjectList(params)
            .then(response => {
              let { data,totalCount,pageSize } = response
              console.log('data',data);
              let tableData = {
                list: data,
                pagination: {
                  pageSize: pageSize,
                  total: totalCount
                }
              }
              // let tableData = this.formatterTableData(data, pagination, totalCount)
              resolve(tableData)
            })
            .catch(() => {
              resolve({
                list: [],
                pagination: { pageSize: 10, total: 0 }
              })
            })
        })
      },
      formatterTableData(tableData, pagination, totalCount) {
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
      handleResetForm() {
        this.$refs.searchForm.resetFields()
        this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
        this.actRepayDate = [], //实际还款日
        this.curRepayDate = [], //应还款日
        this.formModel = {
            receiverName: '', // 商户名称
            payerName: '', //客户名称
            // 支付日期
            startPayDate:this.payDay[0],
            endPayDate:this.payDay[1],
            // 应还日
            startCurRepayDate:'',
            endCurRepayDate:'',
            // 实还日
            startActRepayDate:'',
            endActRepayDate:'',
            businessPayNo:'', //平台订单号
            loanBalanceNo:'', //借据号
            payoffflag:'' //还款状态
        },
        this.pagination = {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        this.handleGetList()
      },
        // 导出excel
        handleExportFile() {
          console.log(this.qs);
          const params = decodeURIComponent(qs.stringify(this.formModel));
          location.href = `/api/pinganLoan/repaymentPlan/export?${params}`
        },
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
  </style>
  