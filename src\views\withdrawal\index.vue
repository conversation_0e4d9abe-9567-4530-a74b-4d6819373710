<template>
  <div class="finance-stagement mt10">
    <ll-list-page-layout v-loading.lock="fullscreenLoading"  element-loading-text="正在导出数据....">
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="业务场景" label-width="90px" prop="businessOrderType">
          <ll-select v-model="formModel.businessOrderType" :options="formItem[0].attrs['options']" @change="changeBusinessOrderType"/>
        </el-form-item>

        <el-form-item label="账户类型" label-width="90px" prop="acounttype">
          <ll-select v-model="formModel.acounttype" :options="formItem[1].attrs['options']" />
        </el-form-item>

        <el-form-item label="账户名称" label-width="90px" prop="acountname">
          <el-input v-model="formModel.acountname" placeholder="账户名称" class="samewidth" />
        </el-form-item>

        <el-form-item label="到账账号" label-width="90px" prop="otherusername">
          <el-input v-model="formModel.otherusername" placeholder="到账账号" class="samewidth" />
        </el-form-item>

        <el-form-item label=" " label-width="90px">
          <el-button type="primary" :loading="isLoading" @click="handleFormSubmit">查询</el-button>
        </el-form-item>
      </el-form>

      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
        :init-fetch="false"
      >
        <template slot="table-column-operation" slot-scope="scope">
          <ll-button type="text" @click.native="withdrawal(scope.row)">提现</ll-button>
          <ll-button type="text" @click="record(scope.row)">提现记录</ll-button>
        </template>
      </ll-data-grid>
    </ll-list-page-layout>

    <withdrawal-dialog :row="rowitem" v-if="withdrawalDialog" :visible.sync="withdrawalDialog" :disabled="disabled" />
    <record-dialog :row="rowitem" v-if="recordDialog" :visible.sync="recordDialog" :disabled="recorddisabled"  />
  </div>
</template>

<script>
import { querywithdrawalOrder } from '@/api/report'
import withdrawalDialog from './components/withdrawalDialog'
import recordDialog from './components/recordDialog'
import { getDictMapQuery } from "@/api/common";
const dataOption = []
export default {
  components: { withdrawalDialog, recordDialog },

  data() {
    return {
      rowitem: {},
      recorddisabled: false,
      recordDialog: false,
      disabled: false,
      withdrawalDialog: false,
      formModel: {
        otherusername: null, //对方账号
        acountname: null, //账户名称
        businessOrderType: '', // 业务场景
        acounttype: '' //账户类型
      },
      formItem: [
        {
          label: '业务场景',
          prop: 'businessOrderType',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '平台账户',
          prop: 'acounttype',
          component: 'll-select',
          attrs: {
            options: [

            ]
          },
          width: '220px'
        }
      ],
      formRules: {
        acountname: [
          {
            type: 'string',
            max: 60,
            message: '账户名称长度在60字符以内',
            trigger: 'blur'
          }
        ],
        otherusername: [
          {
            type: 'string',
            max: 60,
            message: '对方账号长度在60字符以内',
            trigger: 'blur'
          }
        ]
      },
      tableColumns: [
        {
          prop: 'serialNumber',
          label: '序号',
          width: '145'
        },
        {
          prop: 'businessType',
          label: '业务场景',
          width: '250'
        },
         {
          prop: 'accountType',
          label: '账户类型',
          width: '250'
        },
        {
          prop: 'accountName',
          label: '账户名称',
          width: '250'
        },
        {
          prop: 'residual',
          label: '余额',
          width: '250'
        },
        {
          prop: 'settleName',
          label: '到账账户',
          width: '260'
        },
        {
          prop: 'settleAccount',
          label: '到账账号',
          width: '260'
        },
        {
          slotName: 'operation',
          label: '操作',
          width: '160'
        }
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false,
      isLoading: false
    }
  },
  created() {
    // this.accountQuery()
    new Promise((resolve) => {
      getDictMapQuery("fuminWithdrawBusinessTypeMap", "list").then(response => {
        this.formItem[0].attrs.options = response.result;
        resolve(response.result);
      });
    }).then(val => {
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      val.forEach(p => {
        if (dataPermission.includes(p.value)) {
          dataOption.push(p)
        }
      });
      // 附默认值
      this.formItem[1].attrs.options = dataOption[0].childrenList;
      this.formModel.acounttype = dataOption[0].childrenList ? dataOption[0].childrenList[0].value : "";
      this.formModel.businessOrderType = dataOption[0].value;
      this.refreshList();
    });
  },
  methods: {
    // 获取用户信息
    accountQuery() {
      let option = []
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
        dataOption.forEach(p => {
          if (dataPermission.includes(p.value)) {
            option.push(p)
          }
        })
        if(dataPermission.includes('YKQ')) {
            this.formModel.businessOrderType = 'YKQ'
        }
        this.formItem[0].attrs.options = option
        if(option.length === 1){
          this.formModel.businessOrderType = option[0].value
        }
    },
    //   提现
    withdrawal(e) {
      this.rowitem = e
      this.withdrawalDialog = true
    },
    //   提现记录
    record(e) {
      this.rowitem = e
      this.recordDialog = true
    },

    // 格式化数据
    formatterTableData(tableData, pagination, totalCount) {
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
    // 业务类型值变化
    changeBusinessOrderType(type) {
      this.formModel.acounttype = "";
      dataOption.forEach(option => {
        if(option.value == type) {
          // 附默认值
          this.formItem[1].attrs.options = option.childrenList ? option.childrenList : [];
          this.formModel.acounttype = option.childrenList ? option.childrenList[0].value : "";
        }
      });
    },
    // 整理查询参数
    getFormParams(pagination) {
      let params = {
        businessType: this.formModel.businessOrderType, //业务场景
        accountType: this.formModel.acounttype, //账户类型
        accountName: this.formModel.acountname, //账户名称
        settleAccount: this.formModel.otherusername, //对方账号
        page: pagination ? pagination.currentPage : null,
        pageSize: pagination ? pagination.pageSize : null
      }
      for (const key in params) {
        if (this.xyySaasValidator.isNull(params[key])) {
          delete params[key]
        }
      }

      return params
    },

    //表格加载
    fetchData({ pagination }) {
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      if(dataPermission === '') {
        let obj ={
          list: [],
          pagination: { total:  0 }
        }
        return Promise.resolve(obj)
      }

      console.log(pagination)
      let params = this.getFormParams(pagination)
      return new Promise(resolve => {
        querywithdrawalOrder(params)
          .then(response => {
            let { data } = response
            let { totalCount } = response
            let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch((err) => {
            console.log(err)
          })
      })
    },

    // 重新加载表
    refreshList() {
      return this.$refs['dataGrid'].loadData()
    },

    // 查询
     handleFormSubmit() {
      this.$refs.searchForm.validate(async valid => {
        if (valid) {
          this.isLoading = true
          try {
            await this.refreshList()
            this.isLoading = false
          } catch (error) {
            this.isLoading = false
            console.log(error)
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
</style>
