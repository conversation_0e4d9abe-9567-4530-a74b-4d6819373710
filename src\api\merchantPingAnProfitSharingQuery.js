import request from '@/utils/request'

/**
 * 商户平安分润查询页面——查询平安冻结金额
 */
export function queryFrozenAmount(params) {
  return request({
    url: '/pinganLoan/frozenAmount',
    method: 'get',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params
  })
}
/**
 * 商户平安分润查询页面——查询平安可提现金额
 */
export function queryBalance(params) {
  return request({
    url: '/pinganLoan/withdrawableBalance',
    method: 'get',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params
  })
}

/**
 * 商户平安分润查询页面-匹配未分账订单接口
 */
export function queryUnsettle(params) {
  return request({
    url: '/pinganLoan/queryUnsettle',
    method: 'get',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params
  })
}

/**
 * 商户平安分润查询页面-提现至平台佣金接口
 */
export function accountTransfer(data) {
  return request({
    url: '/pinganLoan/accountTransfer',
    method: 'post',
    data
  })
}


/**
 * 商户平安分润查询页面-分账接口
 */
export function settle(data) {
    return request({
      url: '/pinganLoan/settle',
      method: 'post',
      params: data,
      data
    })
  }