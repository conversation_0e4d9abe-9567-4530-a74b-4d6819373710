import { asyncRoutes, constantRoutes } from "@/router";

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role));
  } else {
    return true;
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = [];

  routes.forEach(route => {
    const tmp = { ...route };
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles);
      }
      res.push(tmp);
    }
  });

  return res;
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 * @param routes asyncRouterMap
 * @param roles
 */
function filterAsyncRouter(routes, roles) {
  const res = []
  var routerFilter = (routerItem, name = '', parent) => {
    var parentName = name
    if (routerItem.meta) {
      name += routerItem.meta.title + '_'
      if(routerItem.meta.noStorageMark){
        name += routerItem.meta.noStorageMark + '_'
      }
    }
    if (roles.indexOf(name) > -1 || routerItem.hidden) {
      var obj = { ...routerItem }
      obj.children = []
      parent.push(obj)
      if (routerItem.children) {
        routerItem.children.forEach(item => {
          routerFilter(item, name, obj.children)
        })
      }
    }
    // 排序
    parent.sort((prev, next) => {
      var prevname = parentName
      if (prev.meta) {
        prevname += prev.meta.title + '_'
      }
      const p = roles.indexOf(prevname)
      var nextname = parentName
      if (next.meta) {
        nextname += next.meta.title + '_'
      }
      const n = roles.indexOf(nextname)
      return p - n
    })
  }
  routes.forEach(item => {
    routerFilter(item, '', res)
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
};

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes;
    state.routes = constantRoutes.concat(routes);
    // console.log(state.routes);
  }
};

//const router404 = { path: "*", redirect: "/404", hidden: true };

const actions = {
  generateRoutes({ commit }, data) {
    return new Promise(resolve => {
      const roles = data
      //asyncRoutes.push(router404);
      let accessedRoutes = filterAsyncRouter(asyncRoutes, roles);
      // console.log("accessedRoutes", accessedRoutes);
      commit("SET_ROUTES", accessedRoutes);
      resolve(accessedRoutes);
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
