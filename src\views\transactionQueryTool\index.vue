<template>
  <div class="top_fth mt10" style="">
    <div class="top">
      <el-form label-width="120px" :inline="true">
        <el-form-item label="YBM单号" prop="ybmOrderNo">
          <el-input v-model="ybmOrderNo" />
        </el-form-item>
        <el-form-item label="支付平台单号" prop="payNo">
          <el-input v-model="payNo" />
        </el-form-item>
        <el-form-item label="">
          <ll-button type="primary" @click="queryTradeList">查询</ll-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableSty">
      <el-table :data="tableData1" stripe border v-loading="loading1" style="max-height: 48%; overflow-y: auto;">
        <el-table-column prop="ybmOrderNo" label="YBM单号" width="140"></el-table-column>
        <el-table-column prop="payNo" label="支付平台单号" width="140"></el-table-column>
        <el-table-column prop="merchantName" label="客户名称"></el-table-column>
        <el-table-column prop="companyName" label="商家名称"></el-table-column>
        <el-table-column prop="orgId" label="商家编号"></el-table-column>
        <el-table-column prop="merchantId" label="客户编号"></el-table-column>
        <el-table-column prop="actualPaymentAmount" label="实付金额"></el-table-column>
        <el-table-column prop="createTime" label="创建时间"></el-table-column>
        <el-table-column prop="payTime" label="支付时间"></el-table-column>
      </el-table>

      <el-table :data="tableData2" stripe border v-loading="loading2" style="max-height: calc(48%); overflow-y: auto;">
        <el-table-column prop="businessOrderNo" label="业务单号" width="140"></el-table-column>
        <el-table-column prop="separateNo" label="分账单号" width="140"></el-table-column>
        <el-table-column prop="thirdTradeNo" label="三方交易单号"></el-table-column>
        <el-table-column prop="tradeType" label="交易类型">
          <template slot-scope="scope">
            <span>{{ { 1: "垫资", 2: "分账" }[scope.row.tradeType] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tradeTime" label="交易时间"></el-table-column>
        <el-table-column prop="tradeStatus" label="交易状态">
          <template slot-scope="scope">
            <span>{{ { 1: "处理中 ", 2: "完成" }[scope.row.tradeType] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remark1" label="备注1"></el-table-column>
        <el-table-column prop="remark2" label="备注2"></el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import { queryTradeList } from '@/api/transactionQueryTool'

export default {
  data() {
    return {
      ybmOrderNo: '',
      payNo: '',
      tableData1: [],
      tableData2: [],
      loading1: false,
      loading2: false,
    }
  },
  methods: {
    queryTradeList() {
      if(!this.ybmOrderNo && !this.payNo){
        this.$message.warning('请输入YBM单号或支付平台单号')
        return
      }
      let subData = {
        ybmOrderNo: this.ybmOrderNo,
        payNo: this.payNo
      }
      queryTradeList(subData).then(res => {
        // console.log('查询交易记录', res)
        if(res.code == 1000){
          this.tableData1 = res.result.tradeRecords
          this.tableData2 = res.result.tradeRecordExtends
        }
      })
    },
  }
}
</script>

<style lang='scss' scoped>
.top_fth {
  height: calc(100% - 10px);
  display: flex;
  flex-direction: column;
  // border: 1px solid red;
  // background-color: #fff;
}

.top {
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px 10px 0px 10px;
  width: 100%;
}

.tableSty {
  background-color: #fff;
  box-sizing: border-box;
  padding: 10px;
  width: 100%;
  height: calc(100% - 100px);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-top: 10px;
}
</style>