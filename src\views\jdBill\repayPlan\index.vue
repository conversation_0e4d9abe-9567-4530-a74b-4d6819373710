<template>
    <div class="finance-stagement mt10">
        <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
            <el-form
                ref="searchForm"
                slot="search-form"
                class="statement-search-form"
                label-width="120px"
                :inline="true"
                :model="formModel"
                >
                <el-row :gutter="10">
                    <el-col :span="6">
                        <el-form-item label="借据单号" label-width="90px" prop="loanArNo">
                        <el-input v-model.trim="formModel.loanArNo" placeholder="请输入借据单号" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="订单号" label-width="90px" prop="orderNo">
                        <el-input v-model.trim="formModel.orderNo" placeholder="请输入订单号" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="支付单号" label-width="90px" prop="payNo">
                        <el-input v-model.trim="formModel.payNo" placeholder="请输入支付单号" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="支付时间" label-width="100px" prop="payDay">
                        <el-date-picker
                            v-model="payDay"
                            type="daterange"
                            format="yyyy-MM-dd"
                            value-format="timestamp"
                            :range-separator="'至'"
                            :clearable="true"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="validateFormRequiredItem"
                            >
                        </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="6">
                        <el-form-item label="借据状态" label-width="90px" prop="state">
                        <el-select v-model="formModel.state" placeholder="请选择借据状态">
                            <el-option label="全部" value="" />
                            <el-option label="结清" value="CLEAR" />
                            <el-option label="正常" value="NORMAL" />
                            <el-option label="逾期" value="OVD" />
                        </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户编码" label-width="90px" prop="merchantId">
                        <el-input v-model.trim="formModel.merchantId" placeholder="请输入客户编码" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户名称" label-width="90px" prop="merchantName">
                            <el-input v-model.trim="formModel.merchantName" placeholder="请输入客户名称" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="商户编码" label-width="90px" prop="orgId">
                        <el-input v-model.trim="formModel.orgId" placeholder="请输入商户编码" class="samewidth" clearable/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="商户名称" label-width="90px" prop="orgName">
                        <el-input v-model.trim="formModel.orgName" placeholder="请输入商户名称" class="samewidth" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="是否包含原路退" label-width="110px" prop="isOriginalRoute">
                        <el-select v-model="formModel.isOriginalRoute" placeholder="请选择是否包含原路退" style="width:195px">
                            <el-option label="全部" value="" />
                            <el-option label="包含" :value=1 />
                            <el-option label="不包含" :value=0 />
                        </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="结清日" label-width="100px" prop="duePay">
                        <el-date-picker
                            v-model="duePay"
                            type="daterange"
                            format="yyyy-MM-dd"
                            value-format="timestamp"
                            :range-separator="'至'"
                            :clearable="true"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="dueChangeDateActual"
                            >
                        </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <div style="float:right;display: flex;justify-content: flex-end;">
                        <ll-button type="success" @click="handleDetail">查询还款明细</ll-button>
                        <ll-button type="primary" @click="handleResetForm">重置</ll-button>
                        <ll-button type="primary" @click="handleGetList">查询</ll-button>
                        <ll-button type="primary" @click="handleExportFile">导出</ll-button>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
            <ll-data-grid
            ref="dataGrid"
            :pagination-page-size.sync="pagination.pageSize"
            :pagination-page-sizes="pagination.sizes"
            :table-columns="tableColumns"
            :async-fetch-data-fun="fetchData"
            @row-click="handleRowClick"
            :row-class-name="tableRowClassName"
            />
        </ll-list-page-layout>
        <!-- 导出弹窗 -->
        <el-dialog
            title="导出明细"
            :visible.sync="exportDialogVisible"
            width="500px"
            :close-on-click-modal="false"
        >
        <div class="export-dialog-content">
          <div class="export-tip">请选择支付时间所在月份</div>
          <div class="date-pickers">
            <div class="picker-item">
              <span class="picker-label">年份：</span>
              <el-date-picker
                v-model="exportYear"
                type="year"
                placeholder="选择年份"
                value-format="yyyy"
                style="width: 150px;"
              >
              </el-date-picker>
            </div>
            <div class="picker-item">
              <span class="picker-label">月份：</span>
              <el-select
                v-model="exportMonth"
                placeholder="选择月份"
                style="width: 150px;"
              >
                <el-option
                  v-for="month in 12"
                  :key="month"
                  :label="month.toString().padStart(2, '0')"
                  :value="month.toString().padStart(2, '0')"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExport">确认导出</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
import { getjdBillCredit,exportJdBillData } from '@/api/jdBill.js'
import { parseTime, download } from '@/utils/index.js'
// 引入处理时间戳
import moment from 'moment'
import qs from 'qs';
export default {
name:'',
data() {
  return {
    fullscreenLoading: false,
    payDay:[], // 支付时间
    duePay: [], // 结清日
    formModel: {
        loanArNo: '', // 借据单号
        orderNo: '', //订单号
        payNo:'',//支付单号
        payStartTime:'',// 支付开始日期
        payEndTime:'',// 支付结束日期
        state:"", //借据状态 NORMAL:正常 OVD:逾期 CLEAR:结清 
        isOriginalRoute:"", //是否包含原路退
        merchantId:'',//客户编码
        merchantName:'',//客户名称
        orgId:'',//商户编码
        orgName:'',//商户名称
        clearStartDate:"", //实际开始结清日
        clearEndDate:"", //实际结束结清日
    },
    currentRow: null, // 当前选中行
    pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
    },
    exportDialogVisible: false, // 导出文件弹窗
    exportYear: new Date().getFullYear().toString(),
    exportMonth: (new Date().getMonth() + 1).toString().padStart(2, '0'),
    tableColumns: [
        {
            prop: 'loanArNo',
            label: '借据号',
            'min-width': '200',
        },
        {
            prop: 'orderNo',
            label: '订单号',
            'min-width': '200'
        },
        {
            prop: 'payNo',
            label: '支付单号',
            'min-width': '200'
        },
        {
            prop: 'loanAmount',
            label: '贷款金额',
            'min-width': '200'
        },
        {
            prop: 'instalIntRate',
            label: '利率',
            'min-width': '200'
        },
        {
            prop: 'loanTerm',
            label: '贷款期限',
            'min-width': '200'
        },
        {
            prop: 'payTime',
            label: '支付时间',
            'min-width': '150',
            formatter:(record) =>
            record.payTime ? moment(record.payTime).format('YYYY-MM-DD HH:mm:ss'):'--'
        },
        {
            prop: 'dueDate',
            label: '应还款日',
            'min-width': '150',
            formatter:(record) =>
            record.dueDate ? moment(record.dueDate).format('YYYY-MM-DD HH:mm:ss'):'--'
        },
        {
            prop: 'clearDate',
            label: '结清日',
            'min-width': '150',
            formatter:(record) =>
            record.clearDate ? moment(record.clearDate).format('YYYY-MM-DD HH:mm:ss'):'--'
        },
        {
            prop: 'state',
            label: '借据状态',
            'min-width': '200',
            formatter:(record) =>{
              return {NORMAL:'正常',OVD:'逾期',CLEAR:'结清'}[record.state]
            }
        },
        {
            prop: 'orgId',
            label: '商户编码',
            'min-width': '200'
        },
        {
            prop: 'orgName',
            label: '商户名称',
            'min-width': '200'
        },
        {
            prop: 'merchantId',
            label: '客户编码',
            'min-width': '200'
        },
        {
            prop: 'merchantName',
            label: '客户名称',
            'min-width': '200'
        },
        {
            prop: 'termNo',
            label: '期次号',
            'min-width': '200'
        },
        {
            prop: 'totalAmount',
            label: '应还总额',
            'min-width': '200'
        },
        {
            prop: 'totalPaymentAmount',
            label: '已还总额',
            'min-width': '200'
        },
        {
            prop: 'payableAmount',
            label: '应还本金',
            'min-width': '200'
        },
        {
            prop: 'paidPrinBal',
            label: '已还本金',
            'min-width': '200'
        },
        {
            prop: 'payableInt',
            label: '应还利息',
            'min-width': '200'
        },
        {
            prop: 'paidIntBal',
            label: '已还利息',
            'min-width': '200'
        },
        {
            prop: 'payableOtherAmount',
            label: '应还其它',
            'min-width': '200'
        },
        {
            prop: 'paidOtherAmount',
            label: '已还其它',
            'min-width': '200'
        },
        {
            prop: 'payablePenInt',
            label: '应还罚息',
            'min-width': '200'
        },
        {
            prop: 'paidPenInt',
            label: '已还罚息',
            'min-width': '200'
        },
        {
            prop: 'payableCoreInt',
            label: '核企补贴应还利息',
            'min-width': '200'
        },
        {
            prop: 'paidCoreInt',
            label: '核企补贴已还利息',
            'min-width': '200'
        },
        {
            prop: 'payableCustInt',
            label: '客户自担应还利息',
            'min-width': '200'
        },
        {
            prop: 'paidCustInt',
            label: '客户自担已还利息',
            'min-width': '200'
        },
        {
            prop: 'isOriginalRoute',
            label: '是否包含原路退',
            'min-width': '200',
            formatter:(record) =>{
              return {1:'是',0:'否'}[record.isOriginalRoute]
            }
        },
    ],
  }
 },
created(){
    this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
    this.formModel.payStartTime = this.payDay[0]
    this.formModel.payEndTime = this.payDay[1]
    this.duePay = []
    this.formModel.clearStartDate = ''
    this.formModel.clearEndDate = ''
},
activated() {
    // 组件被激活时重新加载数据
    this.$refs['dataGrid'] && this.$refs['dataGrid'].loadData()
},
methods: {
    handleResetForm() {
        this.$refs.searchForm.resetFields()
        this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
        this.duePay = []
        this.formModel = {
            loanArNo: '', // 借据单号
            orderNo: '', //订单号
            payNo:'',//支付单号
            payStartTime: this.payDay[0],// 支付开始日期
            payEndTime: this.payDay[1],// 支付结束日期
            state:"", //借据状态 NORMAL:正常 OVD:逾期 CLEAR:结清 
            isOriginalRoute:"", //是否包含原路退
            merchantId:'',//客户编码
            merchantName:'',//客户名称
            orgId:'',//商户编码
            orgName:'',//商户名称
            clearStartDate:"", //实际开始结清日
            clearEndDate:"", //实际结束结清日
        }
        this.pagination = {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        this.handleGetList()
    },
    // 订单支付时间
    validateFormRequiredItem(val) {
        if (val) {
            this.formModel.payStartTime = val[0]
            this.formModel.payEndTime = val[1]
        } else {
            this.formModel.payStartTime = ''
            this.formModel.payEndTime = ''
        }
    },
    //实际还款时间
    dueChangeDateActual(val){
        if (val) {
            this.formModel.clearStartDate = val[0]
            this.formModel.clearEndDate = val[1]
        } else {
            this.formModel.clearStartDate = ''
            this.formModel.clearEndDate = ''
        }
    },
    // 支付时间处理函数
    lastMonthFirstDay(){
        const date = new Date()
        date.setMonth(date.getMonth() -1);//获取上一个月
        date.setDate(1);//设置为1号
        return date.getTime();
    },
    handleRowClick(row) {
        this.currentRow = row;
    },
    tableRowClassName({row}) {
        if (this.currentRow && row.payNo === this.currentRow.payNo) {
            return 'current-row';
        }
        return '';
    },
    handleDetail() {
        if (!this.currentRow) {
            this.$message({
                message: '请先选择要查看的数据',
                type: 'warning'
            });
            return;
        }
        
        // 跳转到详情页
        this.$router.replace({
            path: '/jdBill/repayPlan/detail',
            query: {
                payNo: this.currentRow.payNo,
                merchantName:this.currentRow.merchantName,
                loanNo:this.currentRow.loanArNo
            }
        });
    },
    handleGetList() {
        this.$refs['dataGrid'].loadData()
    },
    // 导出excel
    handleExportFile() {
        this.exportDialogVisible = true;
    },
    fetchData({pagination}) {
        console.log(pagination);
        const { pageSize, currentPage } = pagination;
        const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
        return new Promise(resolve => {
          getjdBillCredit(params)
            .then(response => {
              let { data,totalCount } = response
              let tableData = {
                list: data,
                pagination: {
                  pageSize: pageSize,
                  total: totalCount
                }
              }
              resolve(tableData)
            })
            .catch(() => {
              resolve({
                list: [],
                pagination: { pageSize: 10, total: 0 }
              })
            })
        })
    },
    confirmExport() {
        if (!this.exportYear || !this.exportMonth) {
            this.$message.warning('请选择年份和月份');
            return;
        }
        // 使用选择的年月创建日期对象
        const startDate = moment(`${this.exportYear}-${this.exportMonth}-01`);
        const endDate = moment(startDate).endOf('month');
        const params = {
            ...this.formModel,
            payStartTime: startDate.valueOf(), // 转换为时间戳
            payEndTime: endDate.valueOf()      // 转换为时间戳
        };
        this.exportDialogVisible = true;
        // 调用原来的导出逻辑
        const exportName = '金蝶账单还款计划'
        exportJdBillData(params)
        .then(response => {
            let result = response.data
            const dateStr = parseTime(new Date(), '{y}{m}{d}')
            const defExportName = `${exportName}-${dateStr}.xlsx`
            const fileExportName =
            response && response.headers && response.headers['export-filename']
                ? decodeURIComponent(response.headers['export-filename'])
                : defExportName
            download(result, fileExportName)
        })
        .catch(error => {
            console.error('导出失败:', {
            error: error,
            message: error.message,
            response: error.response,
            stack: error.stack,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
            });
            // 根据错误状态显示不同的错误信息
            const errorMsg = error.response?.data?.message 
            || error.response?.statusText 
            || error.message 
            || '导出失败，请稍后重试';
            this.$message.error(errorMsg);
        })
        .finally(() => {
            this.exportDialogVisible = false;
        })
    },
},
}
</script>

<style lang='scss' scoped>
.finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
  ::v-deep .current-row {
    background-color: #ecf5ff !important;
  }
  ::v-deep .el-table__body tr:hover > td {
    background-color: transparent !important;
  }
  ::v-deep .el-dialog {
    .el-dialog__header {
      border-bottom: 1px solid #EBEEF5;
      padding: 15px 20px;
    }
    .el-dialog__body {
      padding: 15px 20px;
    }
  }
  .export-dialog-content {
    .export-tip {
      margin-bottom: 20px;
    }
    .date-pickers {
      display: flex;
      align-items: center;
      .picker-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .picker-label {
          margin-right: 10px;
          white-space: nowrap;
        }
      }
    }
  }
</style>