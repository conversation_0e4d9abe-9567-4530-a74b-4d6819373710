import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/',
    component: Layout,
    redirect: 'dashboard',
    hidden: true,
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index'),
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/redirect/index'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true,
  },
  // {
  //   path: "/example",
  //   component: Layout,
  //   redirect: "/example/table",
  //   name: "Example",
  //   meta: { title: "Example", icon: "example" },
  //   children: [
  //     {
  //       path: "table",
  //       name: "Table",
  //       component: () => import("@/views/table/index"),
  //       meta: { title: "Table", icon: "table" }
  //     },
  //     {
  //       path: "tree",
  //       name: "Tree",
  //       component: () => import("@/views/tree/index"),
  //       meta: { title: "Tree", icon: "tree" }
  //     }
  //   ]
  // },

  // {
  //   path: "/form",
  //   component: Layout,
  //   children: [
  //     {
  //       path: "index",
  //       name: "Form",
  //       component: () => import("@/views/form/index"),
  //       meta: { title: "Form", icon: "form" }
  //     }
  //   ]
  // },

  // {
  //   path: "/nested",
  //   component: Layout,
  //   redirect: "/nested/menu1",
  //   name: "Nested",
  //   meta: {
  //     title: "Nested",
  //     icon: "nested"
  //   },
  //   children: [
  //     {
  //       path: "menu1",
  //       component: () => import("@/views/nested/menu1/index"), // Parent router-view
  //       name: "Menu1",
  //       meta: { title: "Menu1" },
  //       children: [
  //         {
  //           path: "menu1-1",
  //           component: () => import("@/views/nested/menu1/menu1-1"),
  //           name: "Menu1-1",
  //           meta: { title: "Menu1-1" }
  //         },
  //         {
  //           path: "menu1-2",
  //           component: () => import("@/views/nested/menu1/menu1-2"),
  //           name: "Menu1-2",
  //           meta: { title: "Menu1-2" },
  //           children: [
  //             {
  //               path: "menu1-2-1",
  //               component: () =>
  //                 import("@/views/nested/menu1/menu1-2/menu1-2-1"),
  //               name: "Menu1-2-1",
  //               meta: { title: "Menu1-2-1" }
  //             },
  //             {
  //               path: "menu1-2-2",
  //               component: () =>
  //                 import("@/views/nested/menu1/menu1-2/menu1-2-2"),
  //               name: "Menu1-2-2",
  //               meta: { title: "Menu1-2-2" }
  //             }
  //           ]
  //         },
  //         {
  //           path: "menu1-3",
  //           component: () => import("@/views/nested/menu1/menu1-3"),
  //           name: "Menu1-3",
  //           meta: { title: "Menu1-3" }
  //         }
  //       ]
  //     },
  //     {
  //       path: "menu2",
  //       component: () => import("@/views/nested/menu2/index"),
  //       meta: { title: "menu2" }
  //     }
  //   ]
  // },

  // {
  //   path: "external-link",
  //   component: Layout,
  //   children: [
  //     {
  //       path: "https://panjiachen.github.io/vue-element-admin-site/#/",
  //       meta: { title: "External Link", icon: "link" }
  //     }
  //   ]
  // }

  // // 404 page must be placed at the end !!!
  // { path: "*", redirect: "/404", hidden: true }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/statement',
    alwaysShow: true,
    name: 'Finance',
    meta: { title: '财务管理', icon: 'nested' },
    children: [
      {
        path: 'statement',
        name: 'FinanceStatement',
        component: () => import('@/views/finance/statement'),
        meta: { title: '财务报表', icon: 'table' },
      },
      {
        path: 'accountreconcliation',
        name: 'AccountReconcliation',
        component: () => import('@/views/accountreconcliation/index'),
        meta: { title: '账户对账', icon: 'table'},
      },
      {
        path: 'tradingreconcliation',
        name: 'Tradingreconcliation',
        component: () => import('@/views/tradingreconcliation/index'),
        meta: { title: '交易对账', icon: 'table'},
      },
      {
        path: 'withdrawal',
        name: 'withdrawal',
        component: () => import('@/views/withdrawal/index'),
        meta: { title: '提现', icon: 'table' },
      },
      // {
      //   path: 'autocheck',
      //   name: 'autocheck',
      //   component: () => import('@/views/autocheck/index'),
      //   meta: { title: '收款自动对账', icon: 'table' },
      // },
      {
        path: 'payReport',
        name: 'payReport',
        component: () => import('@/views/payReport/index'),
        meta: { title: '支付金额报表', icon: 'table' },
      },
      {
        path: 'queryTrade',
        name: 'queryTrade',
        component: () => import('@/views/queryTrade/index'),
        meta: { title: '交易查询', icon: 'table' },
      },
      {
        path: 'pingAnAccountTransfer',
        name: 'pingAnAccountTransfer',
        component: () => import('@/views/pingAnAccountTransfer/index'),
        meta: { title: '平安账户转账', icon: 'table' },
      },
      {
        path: 'transactiondata',
        name: 'transactiondata',
        component: () => import('@/views/transactiondata/index'),
        meta: { title: '京东交易数据', icon: 'table'},
      },
      {
        path: 'settledata',
        name: 'settledata',
        component: () => import('@/views/liquidation/index'),
        meta: { title: '京东清算数据', icon: 'table'},
      },
      {
        path: 'mergedPaymentQuery',
        name: 'mergedPaymentQuery',
        component: () => import('@/views/mergePayment/mergedPaymentQuery/index'),
        meta: { title: '合并支付交易查询', icon: 'table'},
      },
      {
        path: 'mergedRefundQuery',
        name: 'mergedRefundQuery',
        component: () => import('@/views/mergePayment/mergedRefundQuery/index'),
        meta: { title: '合并支付退款查询', icon: 'table'},
      },
      // {
      //   path: 'memberTrading',
      //   name: 'settlmemberTradingedata',
      //   component: () => import('@/views/memberTrading/index'),
      //   meta: { title: '会员间交易数据', icon: 'table'},
      // },
      {
        path: 'feedata',
        name: 'feedata',
        component: () => import('@/views/serviceCharge/index'),
        meta: { title: '京东支付手续费', icon: 'table'},
      },
      {
        path: 'transactionQueryTool',
        name: 'transactionQueryTool',
        component: () => import('@/views/merchantPingAnProfitSharingQuery/index'),
        meta: { title: '平安余额查询', icon: 'table'},
      },
      {
        path: 'merchantPingAnProfitSharingQuery',
        name: 'merchantPingAnProfitSharingQuery',
        component: () => import('@/views/transactionQueryTool/index'),
        meta: { title: '交易明细查询', icon: 'table'},
      },
    ],
  },
  {
    path: '/openAccount',
    component: Layout,
    alwaysShow: true,
    name: 'openAccount',
    meta: { title: '开户相关', icon: 'nested' },
    children: [
      {
        path: '/queryStatus',
        name: 'queryStatus',
        component: () => import('@/views/openAccount/queryOpenAccountStatus/index'),
        meta: { title: '开户进度查询', icon: 'table' },
      },
      {
        path: '/queryInfo',
        name: 'queryInfo',
        component: () => import('@/views/openAccount/queryOpenAccountInfo/index'),
        meta: { title: '开户信息查询', icon: 'table' },
      },
    ],
  },
  {
    path: '/account',
    component: Layout,
    redirect: '/account/power',
    alwaysShow: true,
    name: 'Account',
    meta: { title: '账号权限', icon: 'nested' },
    children: [
      {
        path: 'power',
        name: 'AccountPower',
        component: () => import('@/views/account/power'),
        meta: { title: '账号权限', icon: 'table'},
      },
    ],
  },
  {
    path: '/pinganLoan',
    component: Layout,
    redirect: '/pinganLoan/repaymentPlan',
    alwaysShow: true,
    name: 'pinganLoan',
    meta: { title: '平安还款计划', icon: 'table' },
    children: [
      {
        path: 'repaymentPlan',
        name: 'repaymentPlan',
        component: () => import('@/views/repaymentPlan/index'),
        meta: { title: '平安还款计划', icon: 'table' }
      }
    ],
  },
  {
    path: '/repayment',
    component: Layout,
    redirect: '/repayment/finance',
    alwaysShow: true,
    name: 'repayment',
    meta: { title: '京东金融还款', icon: 'table' },
    children: [
      {
        path: 'finance',
        name: 'finance',
        component: () => import('@/views/repayment/index'),
        meta: { title: '京东金融还款', icon: 'table' }
      },
      {
        path: 'detail',
        name: 'detail',
        hidden:true,
        component: () => import('@/views/repayment/detail'),
        meta: { title: '借据详情', icon: 'table' }
      }
    ],
  },
  {
    path: '/xyd',
    component: Layout,
    redirect: '/xyd/repayPlan',
    alwaysShow: true,
    name: 'xyd',
    meta: { title: '小雨点', icon: 'table' },
    children: [
      {
        path: '/xyd/repayPlan',
        name: 'XydRepayPlan',
        component: () => import('@/views/xyd/repayPlan/index'),
        meta: { title: '小雨点还款计划', icon: 'table' }
      },
      {
        path: '/xyd/repayPlan/detail',
        name: 'xydrepayPlanDetail',
        hidden:true,
        component: () => import('@/views/xyd/repayPlan/detail'),
        meta: { title: '小雨点还款计划详情', icon: 'table' }
      },
      {
        path: 'xyd/specialAccount',
        name: 'xydspecialAccount',
        component: () => import('@/views/xyd/specialAccount/index'),
        meta: { title: '小雨点专户查询', icon: 'table' }
      }
    ],
  },
  {
    path: '/jdBill',
    component: Layout,
    redirect: '/jdBill/repayPlan',
    alwaysShow: true,
    name: 'jdBill',
    meta: { title: '金蝶', icon: 'table' },
    children: [
      {
        path: '/jdBill/repayPlan',
        name: 'JdBillRepayPlan',
        component: () => import('@/views/jdBill/repayPlan/index'),
        meta: { title: '还款计划', icon: 'table' }
      },
      {
        path: '/jdBill/repayPlan/detail',
        name: 'JdBillRepayPlanDetail',
        hidden:true,
        component: () => import('@/views/jdBill/repayPlan/detail'),
        meta: { title: '还款计划详情', icon: 'table' }
      },
      {
        path: '/jdBill/repayPlanJH',
        name: 'JdBillRepayPlanJH',
        component: () => import('@/views/jdBill/repayPlanJH/index'),
        meta: { title: '还款计划（建行）', icon: 'table' }
      },
    ]
  }
]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
