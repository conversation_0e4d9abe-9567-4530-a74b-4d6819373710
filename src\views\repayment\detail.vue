<template>
    <div class="finance-stagement mt10">
      <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
        <div class="top">
            <el-row :gutter="10" style="margin-left:100px;margin-bottom:10px">
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">借据号</div>
                        <div>{{formModel.loanNo}}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">借款单状态</div>
                        <div>{{formModel.loanStatus}}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">
                            <div style="display:flex;align-items: center;">
                                <div>提前结清时金额</div>
                                <div>
                                    <el-tooltip class="item" effect="dark" content="包含本金金额、利息金额、罚息金额、逾期金额" placement="top-end">
                                        <span class="el-icon-warning-outline"></span>
                                    </el-tooltip> 
                                </div> 
                            </div> 
                        </div>
                        <div>{{formModel.preSettleAmt}}</div>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="10" style="margin-left:100px;margin-bottom:10px">
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">订单号</div>
                        <div>{{formModel.businessOrderNo}}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">
                            <div style="display:flex;align-items: center;">
                                <div>已还金额</div>
                                <div>
                                    <el-tooltip class="item" effect="dark" content="包含本金金额、利息金额、罚息金额、逾期金额" placement="top-end">
                                        <span class="el-icon-warning-outline"></span>
                                    </el-tooltip> 
                                </div> 
                            </div> 
                        </div>
                        <div>{{formModel.paidAmt}}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">日利率</div>
                        <div>{{formModel.dayRate}}</div>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="10" style="margin-left:100px;margin-bottom:10px">
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">借款金额</div>
                        <div>{{formModel.loanAmt}}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">
                            <div style="display:flex;align-items: center;">
                                <div>当前应还金额</div>
                                <div>
                                    <el-tooltip class="item" effect="dark" content="包含本金金额、利息金额、罚息金额、逾期金额" placement="top-end">
                                        <span class="el-icon-warning-outline"></span>
                                    </el-tooltip> 
                                </div> 
                            </div> 
                        </div>
                        <div>{{formModel.currUnPayAmt}}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">年利率</div>
                        <div>{{formModel.yearRate}}</div>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="10" style="margin-left:100px;margin-bottom:10px">
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">借款期数</div>
                        <div>{{formModel.totalPlan}}</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="item">
                        <div class="itemOne">
                            <div style="display:flex;align-items: center;">
                                <div>到期应还金额</div>
                                <div>
                                    <el-tooltip class="item" effect="dark" content="包含本金金额、利息金额、罚息金额、逾期金额" placement="top-end">
                                        <span class="el-icon-warning-outline"></span>
                                    </el-tooltip> 
                                </div> 
                            </div>
                        </div>
                        <div>{{formModel.dueUnPayAmt}}</div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <el-table
          ref="table"
          v-loading="loading"
          :data="dataTable"
          stripe
          border
          highlight-current-row
          :header-cell-style="{background: 'rgb(239, 241, 245)'}"
          style="width: 100%"
          class="lwq"
        >
        <template v-for="(item) in tableColumns">
          <el-table-column
            :key="item.prop"
            :label="item.label"
            :width="item.width"
            :prop="item.prop"
            :formatter="item.formatter"
            show-overflow-tooltip
            align="center"
          >
            <template  slot="header">
              <el-tooltip effect="dark" placement="top" v-if="item.hint">
                <div slot="content">
                  <template v-for="el in item.hint">
                    <span :key="el"> {{ el }} <br /></span>
                  </template>
                </div>
                <span class="yz lwqTableLabel">{{ item.label }}<i class="el-icon-warning-outline" /></span>
              </el-tooltip>
              <div v-else class="yz lwqTableLabel">
                  {{ item.label }}
              </div>
            </template>
            <template v-if="item.prop == 'orderPlanNo'" v-slot="{ row }">
                <el-link type="primary" class="lwqLog" :underline="false" @click="$router.push({ path: '/supply/purchasePlan/purchasePlanDetail', query: { orderPlanNo: row.orderPlanNo } })">
                {{row.orderPlanNo}}
                </el-link>
            </template>
            <template v-else-if="item.prop == 'orderNo'" v-slot="{ row }">
                <el-link type="primary" class="lwqLog" :underline="false" @click="linkPurchaseDetail(row)">{{row.orderNo }}</el-link>
            </template>
            <template v-else-if="item.prop == 'orderStatus'" v-slot="{ row }">
                <el-tag class="lwqLogTag" :type="orderStatusColor(row.orderStatus)">
                  {{ currentStatusText(row.orderStatus) }}
                </el-tag>
            </template>
          </el-table-column>
        </template>
        </el-table>
      </ll-list-page-layout>
    </div>
  </template>
  
  <script>
  import {getjdDetail} from '@/api/openAccount.js'
  import { parseTime, download } from '@/utils/index.js'
  import {Message} from "element-ui"
  import qs from 'qs';
  // 引入处理时间戳
  import moment from 'moment'
  export default {
    data() {
      return {
        qs,
        payDay:[],
        actRepayDate:[], //实际还款日
        curRepayDate:[], //应还款日
        formModel: {
            loanNo: '', // 借据单号
            loanStatus:'',//借款单状态
            preSettleAmt:'',//提前结清时金额
            businessOrderNo: '', //订单号
            paidAmt:'',//已还金额
            dayRate:'',//日利率
            loanAmt:'',//借款金额
            currUnPayAmt:'',//当前应还金额
            yearRate:'',//年利率
            totalPlan:'',//借款期数
            dueUnPayAmt:'',//到期应还金额
        },
        dataTable:[],
        loading:false,
        tableColumns: [
          {
            prop: 'termNo',
            label: '期号',
            width: '220'
          },
          {
            prop: 'currTerm',
            label: '是否为当前期',
            width: '100'
          },
          {
            prop: 'planStatus',
            label: '分期单状态',
            width: '120'
          },
          {
            prop: 'startDate',
            label: '分期单开始时间',
            width: '200'
          },
          {
            prop: 'endDate',
            label: '分期单结束时间',
            width: '200'
          },
          {
            prop: 'finishDate',
            label: '结清时间',
            width: '200'
          },
          {
            prop: 'overDueStartDate',
            label: '逾期开始时间',
            width: '200'
          },
          {
            prop: 'paidAmt',
            label: '分期单已还金额',
            width: '200',
            hint:['包含本金金额、利息金额、罚息金额、逾期金额']
          },
          {
            prop: 'currUnPayAmt',
            label: '分期单当前应还金额',
            width: '200',
            hint:['包含本金金额、利息金额、罚息金额、逾期金额']
          },
          {
            prop: 'dueUnPayAmt',
            label: '分期单到期应还金额',
            width: '200',
            hint:['包含本金金额、利息金额、罚息金额、逾期金额']
          },
        ],
        fullscreenLoading: false,
        payNo:'',
      }
    },
    created(){
      if (this.$route.query.payNo) {
          this.payNo = this.$route.query.payNo;
          this.getDetailData(this.$route.query.payNo);
        }
    },
    computed: {
    },
    mounted() {},
    methods: {
      /**获取详情 */
      getDetailData(payNo){
        let formData = new FormData()
        formData.append('payNo',payNo)
        getjdDetail(formData).then(response => {
          let { result,code,msg} = response
          if(code == '1000'){
            Object.keys(this.formModel).forEach((key) => {
            this.formModel[key] = result[key];
            this.dataTable = result.detailList;
          });
          }else{
            Message({
              message: msg,
              type: "error",
              duration: 5 * 1000
            });
          }
        }).catch(() => {
        })
      },
    }
  }
  </script>
  
  <style lang="scss" scoped>
    .top{
        margin-bottom: 50px;
    }
  .item{
    display: flex;
    .itemOne {
        flex-grow:0.1;
    }
  }
  .finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
  </style>
  