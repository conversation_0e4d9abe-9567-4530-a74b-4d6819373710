import router from "./router";
import store from "./store";
// import { Message } from "element-ui";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
// import { getToken } from "@/utils/auth"; // get token from cookie
import getPageTitle from "@/utils/get-page-title";
import { MessageBox } from "element-ui";

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = ["/login", "/auth-redirect"]; // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start();

  // set page title
  document.title = getPageTitle(to.meta.title);

  // // determine whether the user has logged in
  // const hasToken = getToken();
  const hasToken = true;

  if (hasToken) {
    const nextRouterList = ["/404", "/401"];
    if (nextRouterList.indexOf(to.path) !== -1) {
      next();
    } else {
      // determine whether the user has obtained his permission roles through getInfo
      const hasRoles = store.getters.roles && store.getters.roles.length > 0;
      if (hasRoles) {
        next();
      } else {
        try {
          // get user info
          // note: roles must be a object array! such as: ['admin'] or ,['developer','editor']
          // const { roles } =
          //const { roles } = await store.dispatch('GetUserRoles')
          const { roles } = await store.dispatch("user/getAuthorUserInfo");
          await store.dispatch("user/getInfo");

          // generate accessible routes map based on roles
          const accessRoutes = await store.dispatch(
            "permission/generateRoutes",
            roles
          );

          // dynamically add accessible routes
          router.addRoutes(accessRoutes);

          // hack method to ensure that addRoutes is complete
          // set the replace: true, so the navigation will not leave a history record
          if(roles.length === 0) {
            // next('/401') 
            MessageBox.confirm('没有权限访问页面', "提示", {
              confirmButtonText: "确认",
              showClose: false,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              showCancelButton: false,
              cancelButtonText: "取消",
              type: "warning"
            }).then(() => {
              store.dispatch("user/logout").then(() => {
                location.reload();
              });
            });
          } else {
            next({ ...to, replace: true });
          }
        } catch (error) {
          // // remove token and go to login page to re-login
          // await store.dispatch("user/resetToken");
          // Message.error(error || "Has Error");
          // next(`/login?redirect=${to.path}`);
          NProgress.done();
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next();
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  // finish progress bar
  NProgress.done();
});
