<template>
  <div class="finance-stagement mt10">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        >
        <!-- :rules="formRules" -->
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="账户类型" label-width="98px" prop="accountType">
            <el-select v-model="formModel.accountType" placeholder="请选择账户类型">
              <el-option label="全部" value="" />
              <el-option label="退款专户" :value=2 />
              <el-option label="贴息专户" :value=3 />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="交易日期" label-width="100px" prop="payDay">
            <el-date-picker
              v-model="payDay"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="timestamp"
              :range-separator="'至'"
              :clearable="true"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="validateFormRequiredItem"
              >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单支付单号" label-width="98px" prop="orderPayNo">
            <el-input v-model.trim="formModel.orderPayNo" placeholder="请输入订单支付单号" class="samewidth" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="退款单支付单号" label-width="110px" prop="refundPayNo">
            <el-input v-model.trim="formModel.refundPayNo" placeholder="请输入退款单支付单号" class="samewidth" style="width:195px"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="商城订单号" label-width="98px" prop="orderNo">
            <el-input v-model.trim="formModel.orderNo" placeholder="请输入商城订单号" class="samewidth" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商城退款单号" label-width="98px" prop="refundNo">
            <el-input v-model.trim="formModel.refundNo" placeholder="请输入商城退款单号" class="samewidth" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="收支类型" label-width="98px" prop="inOutType">
            <el-select v-model="formModel.inOutType" placeholder="请选择收支类型">
              <el-option label="全部" value="" />
              <el-option label="收入" :value=1 />
              <el-option label="支出" :value=2 />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户编码" label-width="98px" prop="merchantId">
            <el-input v-model.trim="formModel.merchantId" placeholder="请输入客户编码" class="samewidth" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="客户名称" label-width="98px" prop="merchantName">
              <el-input v-model.trim="formModel.merchantName" placeholder="请输入客户名称" class="samewidth" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商户编码" label-width="98px" prop="orgId">
            <el-input v-model.trim="formModel.orgId" placeholder="请输入商户编码" class="samewidth" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
            <el-form-item label="商户名称" label-width="98px" prop="orgName">
              <el-input v-model.trim="formModel.orgName" placeholder="请输入商户名称" class="samewidth" />
            </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="交易类型" label-width="98px" prop="tradeType">
            <el-select v-model="formModel.tradeType" placeholder="请选择交易类型">
              <el-option label="全部" value="" />
              <el-option label="预估款充值" :value=14 />
              <el-option label="保证金充值" :value=16 />
              <el-option label="保证金缺口充值" :value=17 />
              <el-option label="日常还款" :value=23 />
              <el-option label="按期还款" :value=24 />
              <el-option label="部分提前还款" :value=25 />
              <el-option label="提前全额结清" :value=26 />
              <el-option label="退货" :value=27 />
              <el-option label="回购" :value=28 />
              <el-option label="代偿" :value=29 />
              <el-option label="资金结算退款" :value=46 />
              <el-option label="记账宝充值" :value=52 />
              <el-option label="转账" :value=54 />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <div style="float:right;display: flex;justify-content: flex-end;">
            <ll-button type="primary" @click="handleResetForm">重置</ll-button>
            <ll-button type="primary" @click="handleGetList">查询</ll-button>
            <ll-button type="primary" @click="handleExportFile">导出</ll-button>
          </div>
        </el-col>
      </el-row>
      </el-form>
      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
        @row-click="handleRowClick"
        :row-class-name="tableRowClassName"
      >
      </ll-data-grid>
    </ll-list-page-layout>

    <!-- 导出弹窗 -->
    <el-dialog
      title="导出明细"
      :visible.sync="exportDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="export-dialog-content">
        <div class="export-tip">请选择交易日期所在月份</div>
        <div class="date-pickers">
          <div class="picker-item">
            <span class="picker-label">年份：</span>
            <el-date-picker
              v-model="exportYear"
              type="year"
              placeholder="选择年份"
              value-format="yyyy"
              style="width: 150px;"
            >
            </el-date-picker>
          </div>
          <div class="picker-item">
            <span class="picker-label">月份：</span>
            <el-select
              v-model="exportMonth"
              placeholder="选择月份"
              style="width: 150px;"
            >
              <el-option
                v-for="month in 12"
                :key="month"
                :label="month.toString().padStart(2, '0')"
                :value="month.toString().padStart(2, '0')"
              />
            </el-select>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport">确认导出</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getAccountList,exportAccountList} from '@/api/xyd.js'
import { parseTime, download } from '@/utils/index.js'
import qs from 'qs';
// 引入处理时间戳
import moment from 'moment'
export default {
  data() {
    return {
      qs,
      payDay:[],
      actRepayDate:[], //实际还款日
      curRepayDate:[], //应还款日
      selectedRows: [], // 添加选中行的数据
      formModel: {
        accountType:"", //账户类型
        tradeStartDate:"",//交易开始日期
        tradeEndDate:"",//交易结束日期
        orderPayNo:"",//订单支付单号
        refundPayNo:"",//退款单支付单号
        orderNo:"",//商城订单号
        refundNo:"",//商城退款单号
        inOutType:"",//收支类型
        orgId:'',//商户编码
        orgName:'',//商户名称
        merchantId:'',//客户编码
        merchantName:'',//客户名称
        tradeType:"",//交易类型
      },
      tableColumns: [
        {
          prop: 'accountType',
          label: '账户类型',
          'min-width': '200',
        },
        {
          prop: 'tradeAccount',
          label: '交易账号',
          'min-width': '200',
        },
        {
          prop: 'tradeCode',
          label: '交易编号',
          'min-width': '200',
        },
        {
          prop: 'tradeDate',
          label: '交易日期',
          'min-width': '200',
        },
        {
          prop: 'tradeType',
          label: '交易类型',
          'min-width': '200',
        },
        {
          prop: 'orderPayNo',
          label: '订单支付单号',
          'min-width': '200',
        },
        {
          prop: 'refundPayNo',
          label: '退款单支付单号',
          'min-width': '200',
        },
        {
          prop: 'orderNo',
          label: '商城订单号',
          'min-width': '200',
        },
        {
          prop: 'refundNo',
          label: '商城退款单号',
          'min-width': '200',
        },
        {
          prop: 'inOutType',
          label: '收支类型',
          'min-width': '200',
        },
        {
          prop: 'tradeAmt',
          label: '交易金额',
          'min-width': '200',
        },
        {
          prop: 'tradeRivalAccount',
          label: '交易对手账号',
          'min-width': '200',
        },
        {
          prop: 'tradeRivalName',
          label: '交易对手名称',
          'min-width': '200',
        },
        {
          prop: 'orgId',
          label: '商户编码',
          'min-width': '200'
        },
        {
          prop: 'orgName',
          label: '商户名称',
          'min-width': '200'
        },
        {
          prop: 'merchantId',
          label: '客户编码',
          'min-width': '200'
        },
        {
          prop: 'merchantName',
          label: '客户名称',
          'min-width': '200'
        },
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false,
      currentRow: null,
      exportDialogVisible: false,
      exportYear: new Date().getFullYear().toString(),
      exportMonth: (new Date().getMonth() + 1).toString().padStart(2, '0'),
    }
  },
  created(){
    this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
    this.formModel.tradeStartDate = this.payDay[0]
    this.formModel.tradeEndDate = this.payDay[1]
  },
  computed: {
  },
  mounted() {},
  methods: {
    /** 跳转借据详情 */
    handleRowClick(row) {
      this.currentRow = row;
    },
    tableRowClassName({row}) {
      if (this.currentRow && row.payNo === this.currentRow.payNo) {
        return 'current-row';
      }
      return '';
    },
    lastMonthFirstDay(){
      const date = new Date()
      date.setMonth(date.getMonth() -1);//获取上一个月
      date.setDate(1);//设置为1号
      return date.getTime();
    },
    // 订单支付时间
    validateFormRequiredItem(val) {
      if (val) {
        this.formModel.tradeStartDate = val[0]
        this.formModel.tradeEndDate = val[1]
      } else {
        this.formModel.tradeStartDate = ''
        this.formModel.tradeEndDate = ''
      }
    },
    handleGetList() {
      this.$refs['dataGrid'].loadData()
    },
    fetchData({pagination}) {
      console.log(pagination);
      const { pageSize, currentPage } = pagination;
      const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
      return new Promise(resolve => {
        getAccountList(params)
          .then(response => {
            let { data,totalCount } = response
            let tableData = {
              list: data,
              pagination: {
                pageSize: pageSize,
                total: totalCount
              }
            }
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },
    formatterTableData(tableData, pagination, totalCount) {
    let reportQueryOrderViewBos = tableData || []
    const { currentPage, pageSize } = pagination

    reportQueryOrderViewBos.map((item, index) => {
      let serialNumber = (currentPage - 1) * pageSize + index + 1
      item.serialNumber = serialNumber
      return item
    })

    let result = {
      list: reportQueryOrderViewBos,
      pagination: { total: totalCount || 0 }
    }

    return result
  },
    handleResetForm() {
      this.$refs.searchForm.resetFields()
      this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
      this.formModel = {
          accountType:"", //账户类型
          tradeStartDate:this.payDay[0],//交易开始日期
          tradeEndDate:this.payDay[1],//交易结束日期
          orderPayNo:"",//订单支付单号
          refundPayNo:"",//退款单支付单号
          orderNo:"",//商城订单号
          refundNo:"",//商城退款单号
          inOutType:"",//收支类型
          orgId:'',//商户编码
          orgName:'',//商户名称
          merchantId:'',//客户编码
          merchantName:'',//客户名称
          tradeType:"",//交易类型
      },
      this.pagination = {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      this.handleGetList()
    },
      // 导出excel
      handleExportFile() {
        this.exportDialogVisible = true;
      },
      confirmExport() {
        if (!this.exportYear || !this.exportMonth) {
          this.$message.warning('请选择年份和月份');
          return;
        }
        // 使用选择的年月创建日期对象
        const startDate = moment(`${this.exportYear}-${this.exportMonth}-01`);
        const endDate = moment(startDate).endOf('month');
        const params = {
          tradeStartDate: startDate.valueOf(), // 转换为时间戳
          tradeEndDate: endDate.valueOf()      // 转换为时间戳
        };
        // 调用原来的导出逻辑
        const exportName = '小雨点专户查询'
        exportAccountList(params)
          .then(response => {
            console.log(response);
            let result = response.data
            const dateStr = parseTime(new Date(), '{y}{m}{d}')
            const defExportName = `${exportName}-${dateStr}.xlsx`
            const fileExportName =
              response && response.headers && response.headers['export-filename']
                ? decodeURIComponent(response.headers['export-filename'])
                : defExportName
            download(result, fileExportName)
          })
          .catch(error => {
              console.error('导出失败:', {
                error: error,
                message: error.message,
                response: error.response,
                stack: error.stack,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data
              });
              // 根据错误状态显示不同的错误信息
              const errorMsg = error.response?.data?.message 
                || error.response?.statusText 
                || error.message 
                || '导出失败，请稍后重试';
              this.$message.error(errorMsg);
            })
          .finally(() => {
            this.exportDialogVisible = false;
          })
      },
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
::v-deep .current-row {
  background-color: #ecf5ff !important;
}
::v-deep .el-table__body tr:hover > td {
  background-color: transparent !important;
}
::v-deep .el-dialog {
  .el-dialog__header {
    border-bottom: 1px solid #EBEEF5;
    padding: 15px 20px;
  }
  .el-dialog__body {
    padding: 15px 20px;
  }
}
.export-dialog-content {
  .export-tip {
    margin-bottom: 20px;
  }
  .date-pickers {
    display: flex;
    align-items: center;
    .picker-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      .picker-label {
        margin-right: 10px;
        white-space: nowrap;
      }
    }
  }
}
</style>
