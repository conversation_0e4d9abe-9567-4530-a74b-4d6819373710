<template>
    <div class="finance-stagement mt10">
      <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
        <el-form
          ref="searchForm"
          slot="search-form"
          class="statement-search-form"
          label-width="120px"
          :inline="true"
          :model="formModel"
          >
          <!-- :rules="formRules" -->
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="借据单号" label-width="90px" prop="loanNo">
              <el-input v-model.trim="formModel.loanNo" placeholder="请输入借据单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单号" label-width="90px" prop="businessOrderNo">
              <el-input v-model.trim="formModel.businessOrderNo" placeholder="请输入订单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付单号" label-width="90px" prop="payNo">
              <el-input v-model.trim="formModel.payNo" placeholder="请输入支付单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付时间" label-width="100px" prop="payDay">
              <el-date-picker
                v-model="payDay"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="timestamp"
                :range-separator="'至'"
                :clearable="true"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="validateFormRequiredItem"
                >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="单据类型" label-width="90px" prop="orderType">
              <el-select v-model="formModel.orderType" placeholder="请选择单据类型">
                <el-option label="订单" :value="1" />
                <el-option label="退款单" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户编码" label-width="90px" prop="custCode">
              <el-input v-model.trim="formModel.custCode" placeholder="请输入客户编码" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称" label-width="90px" prop="custName">
                <el-input v-model.trim="formModel.custName" placeholder="请输入客户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商户编码" label-width="90px" prop="sellerId">
              <el-input v-model.trim="formModel.sellerId" placeholder="请输入商户编码" class="samewidth" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
            <el-col :span="6">
            <el-form-item label="商户名称" label-width="90px" prop="sellerId">
              <el-input v-model.trim="formModel.sellerName" placeholder="请输入商户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <div style="float:right;">
              <ll-button type="primary" @click="handleResetForm">重置</ll-button>
              <ll-button type="primary" @click="handleGetList">查询</ll-button>
              <ll-button type="primary" style="margin-right:50px;" @click="handleExportFile">导出</ll-button>
            </div>
          </el-col>
        </el-row>
        </el-form>
        <ll-data-grid
          ref="dataGrid"
          :pagination-page-size.sync="pagination.pageSize"
          :pagination-page-sizes="pagination.sizes"
          :table-columns="tableColumns"
          :async-fetch-data-fun="fetchData"
        >
        <template slot="table-column-loanNo" slot-scope="scope">
            <template>
                <el-link type="primary" style="text-decoration: none;" @click="goDetail(scope.row)">{{scope.row.loanNo}}</el-link>
            </template>
        </template>
        </ll-data-grid>
      </ll-list-page-layout>
    </div>
  </template>
  
  <script>
  import {getjdCredit,exportjdList} from '@/api/openAccount.js'
  import { parseTime, download } from '@/utils/index.js'
  import qs from 'qs';
  // 引入处理时间戳
  import moment from 'moment'
  export default {
    data() {
      return {
        qs,
        payDay:[],
        actRepayDate:[], //实际还款日
        curRepayDate:[], //应还款日
        formModel: {
            loanNo: '', // 借据单号
            businessOrderNo: '', //订单号
            payNo:'',//支付单号
            startPayDate:'',// 支付开始日期
            endPayDate:'',// 支付结束日期
            custCode:'',//客户编码
            custName:'',//客户名称
            sellerId:'',//商户编码
            sellerName:'',//商户名称
            orderType:'',//单据类型
        },
        tableColumns: [
          {
            prop: 'loanNo',
            label: '借据号',
            'min-width': '200',
            slotName:'loanNo',
          },
          {
            prop: 'businessOrderNo',
            label: '订单号',
            'min-width': '200'
          },
          {
            prop: 'businessRefundNo',
            label: '退款单号',
            'min-width': '200'
          },
          {
            prop: 'orderType',
            label: '单据类型',
            'min-width': '200',
          },
          {
            prop: 'payNo',
            label: '支付单号',
            'min-width': '200'
          },
          {
            prop: 'custCode',
            label: '客户编码',
            'min-width': '200'
          },
          {
            prop: 'custName',
            label: '客户名称',
            'min-width': '200'
          },
          {
            prop: 'sellerId',
            label: '商户编码',
            'min-width': '200'
          },
          {
            prop: 'sellerName',
            label: '商户名称',
            'min-width': '200'
          },
          {
            prop: 'payTime',
            label: '支付时间',
            'min-width': '150',
            formatter:(record) =>
            record.payTime ? moment(record.payTime).format('YYYY-MM-DD HH:mm:ss'):'--'
          },
          {
            prop: 'payAmount',
            label: '订单实付金额',
            'min-width': '150'
          },
          {
            prop: 'payCashAmount',
            label: '订单现金实付',
            'min-width': '150'
          },
          {
            prop: 'virtualGoldAmount',
            label: '购物金实付',
            'min-width': '150'
          },
          {
            prop: 'refundPayAmount',
            label: '退款金额',
            'min-width': '150'
          },
          {
            prop: 'refundPayCashAmount',
            label: '现金实退金额',
            'min-width': '150'
          },
          {
            prop: 'refundVirtualGoldAmount',
            label: '购物金实退金额',
            'min-width': '150'
          },
        ],
        pagination: {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        fullscreenLoading: false,
      }
    },
    created(){
      this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
      this.formModel.startPayDate = this.payDay[0]
      this.formModel.endPayDate = this.payDay[1]
    },
    computed: {
    },
    mounted() {},
    methods: {
      /** 跳转借据详情 */
      goDetail(row){
        this.$router.push({
            path: '/repayment/detail', 
            query: {
                payNo: row.payNo,
            }
        })
      },
      lastMonthFirstDay(){
        const date = new Date()
        date.setMonth(date.getMonth() -1);//获取上一个月
        date.setDate(1);//设置为1号
        return date.getTime();
      },
      // 订单支付时间
      validateFormRequiredItem(val) {
        if (val) {
          this.formModel.startPayDate = val[0]
          this.formModel.endPayDate = val[1]
        } else {
          this.formModel.startPayDate = ''
          this.formModel.endPayDate = ''
        }
      },
      handleGetList() {
        this.$refs['dataGrid'].loadData()
      },
      fetchData({pagination}) {
        console.log(pagination);
        const { pageSize, currentPage } = pagination;
        const params = Object.assign({ pageSize : pageSize, page:currentPage},this.formModel);
        return new Promise(resolve => {
          getjdCredit(params)
            .then(response => {
              let { result } = response
              let tableData = {
                list: result.list,
                pagination: {
                  pageSize: pageSize,
                  total: result.total
                }
              }
              resolve(tableData)
            })
            .catch(() => {
              resolve({
                list: [],
                pagination: { pageSize: 10, total: 0 }
              })
            })
        })
      },
      formatterTableData(tableData, pagination, totalCount) {
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
      handleResetForm() {
        this.$refs.searchForm.resetFields()
        this.payDay = [this.lastMonthFirstDay(),new Date().getTime()]
        this.formModel = {
            loanNo: '', // 借据单号
            businessOrderNo: '', //订单号
            payNo:'',//支付单号
            startPayDate:'',// 支付开始日期
            endPayDate:'',// 支付结束日期
            custCode:'',//客户编码
            custName:'',//客户名称
            sellerId:'',//商户编码
            sellerName:'',//商户名称
            orderType:'',//单据类型
        },
        this.pagination = {
          pageSize: 10,
          sizes: [10, 20, 30, 50, 100]
        },
        this.handleGetList()
      },
        // 导出excel
        handleExportFile() {
          // console.log(this.qs);
          // const params = decodeURIComponent(qs.stringify(this.formModel));
          // location.href = `/api/pinganLoan/repaymentPlan/export?${params}`
          const exportName = '京东金融还款'
          exportjdList({...this.formModel})
            .then(response => {
              console.log(response);
              let result = response.data
              const dateStr = parseTime(new Date(), '{y}{m}{d}')
              const defExportName = `${exportName}-${dateStr}.xlsx`
              const fileExportName =
                response && response.headers && response.headers['export-filename']
                  ? decodeURIComponent(response.headers['export-filename'])
                  : defExportName
              download(result, fileExportName)
            })
            .finally(() => {
              // this.fullscreenLoading = false
            })
        },
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .finance-stagement {
    .statement-search-form {
      .el-form-item {
        width: 320px;
        .el-form-item__content {
          .el-select {
            width: 220px;
          }
          .el-range-editor {
            width: 220px;
          }
          width: 220px;
        }
      }
    }
  }
  .finance-stagement {
    height: calc(100% - 10px);
  }
  .samewidth {
    width: 220px;
  }
  </style>
  