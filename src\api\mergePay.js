import request from '@/utils/request'
import requestDownload from '@/utils/requestDownload'

/**
 * 支付后台_合并支付交易查询
 */
export function getMergePayList(data) {
  return request({
    url: '/paymentMerge/getPageInfo',
    method: 'post',
    data
  })
}

/**
 * 支付后台_合并支付交易查询导出

 */
export function exportMergePay(data) {
  return request({
    url: '/paymentMerge/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

/**
 * 支付后台_合并支付退款查询
 */
export function getRefundList(data) {
  return request({
    url: '/paymentMerge/getPageInfoForRefund',
    method: 'post',
    data
  })
}

/**
 * 支付后台_合并支付退款查询导出

 */
export function exportRefundPay(data) {
  return request({
    url: '/paymentMerge/exportForRefund',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

/**
 * 支付后台_合并支付获取支付渠道
 */
export function getChannelEnum() {
  return request({
    url: '/paymentMerge/getChannelEnum',
    method: 'post',
    data: {}
  })
}