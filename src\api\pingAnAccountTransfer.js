import request from '@/utils/request';

/**
 * 查询账户接口：api/account/accountList
 */
export function accountList(data) {
  return request({
    url: '/account/accountList',
    method: 'get',
    data
  })
}

/**
 * 查询列表接口：api/account/accountTransferList
 */
export function accountTransferList(data) {
  return request({
    url: '/account/accountTransferList',
    method: 'post',
    data
  })
}

/**
 * 发起转账接口：api/account/accountTransfer
 */
export function accountTransfer(data) {
  return request({
    url: '/account/accountTransfer',
    method: 'post',
    data
  })
}

/**
 * 查询余额：/api/account/queryAccountBalance
 */
export function queryAccountBalance(data) {
  return request({
    url: '/account/queryAccountBalance',
    method: 'post',
    data
  })
}
