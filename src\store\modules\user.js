/* eslint-disable prettier/prettier */
import { userLogin, userLogout } from "@/api/user";
import { accountQuery } from "@/api/account";
import { getToken, setToken, removeToken } from "@/utils/auth";
import router, { resetRouter } from "@/router";

const state = {
  token: getToken(),
  name: "",
  departmentname: "",
  avatar: "",
  introduction: "",
  roles: [],
  menuPermission: '',
  dataPermission: ''
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_DEPARTMENTNAME: (state, name) => {
    state.departmentname = name;
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
  },
  SET_MENUPERMISSION: (state, menuPermission) => {
    state.menuPermission = menuPermission;
  },
  SET_DATAPERMISSION: (state, dataPermission) => {
    state.dataPermission = dataPermission;
  }
};

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo;
    return new Promise((resolve, reject) => {
      userLogin({ username: username.trim(), password: password })
        .then(response => {
          const { data } = response;
          commit("SET_TOKEN", data.token);
          setToken(data.token);
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // 鉴权获取用户信息
  getAuthorUserInfo({ commit }) {
    // const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      accountQuery()
        .then(response => {
          const { result } = response;
          // console.log('response: ', response)
          const { account, position, departmentName, avatar, dataPermission, menuPermission } = result;
          window.localStorage.setItem(
            "userInfo",
            JSON.stringify({
              SET_NAME: account,
              SET_AVATAR: avatar || "",
              SET_DEPARTMENTNAME: departmentName || "",
              SET_INTRODUCTION: position || "",
              menuPermission: JSON.stringify(menuPermission) || '',
              dataPermission: dataPermission || ''
            })
          );
          commit("SET_TOKEN", "token");
          commit('SET_MENUPERMISSION', JSON.stringify(menuPermission) || '')
          commit('SET_DATAPERMISSION', dataPermission || '')
          setToken("token");
          let roles = []

          //获取完整的节点按钮名称拼接成唯一标识
          const getRoleName = (roleObj, name = '') => {
            if (!roleObj.parentId) {
              name = ''
            }
            name += roleObj.name + '_'
            roles.push(name)
            if (roleObj.children && roleObj.children.length > 0) {
              roleObj.children.forEach(item => {
                getRoleName(item, name)
              })
            }
          }
          result.menuPermission&&result.menuPermission.forEach(item => {
            getRoleName(item)
          })
          commit('SET_ROLES', roles)
          resolve({ roles })
        })
        .catch(error => {
          console.log('accountQuery error: ', error)
          reject(error);
        });
    });
  },

  // get user info
  getInfo({ commit }) {
    return new Promise(resolve => {
      const userInfo = window.localStorage.getItem("userInfo");
      const { SET_NAME, SET_AVATAR, SET_INTRODUCTION, SET_DEPARTMENTNAME } = JSON.parse(
        userInfo
      );
      //commit("SET_ROLES", ["other"]);
      commit("SET_NAME", SET_NAME);
      commit("SET_AVATAR", SET_AVATAR);
      commit("SET_DEPARTMENTNAME", SET_DEPARTMENTNAME);
      commit("SET_INTRODUCTION", SET_INTRODUCTION);
      resolve();
    });
  },
  /*GetUserRoles({ commit, state }) {
    return new Promise((resolve, reject) => {
      getZBPermissionTreeByEmployeeId()
        .then(data => {
          let roles = []
          if (!data) {
            return false
          }
          //获取完整的节点按钮名称拼接成唯一标识
          const getRoleName = (roleObj, name = '') => {
            if (roleObj.parentId == 0) {
              name = ''
            }
            name += roleObj.name + '_'
            roles.push(name)
            if (roleObj.childPermissionDtos && roleObj.childPermissionDtos.length > 0) {
              roleObj.childPermissionDtos.forEach(item => {
                getRoleName(item, name)
              })
            }
          }
          data.result.forEach(item => {
            getRoleName(item)
          })
          commit('SET_ROLES', roles)
          resolve({ roles })
        })
        .catch(error => {
          reject(error)
        })
    })
  },*/
  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      userLogout(state.token)
        .then(() => {
          commit("SET_TOKEN", "");
          commit("SET_ROLES", []);
          removeToken();
          resetRouter();

          // reset visited views and cached views
          // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
          dispatch("tagsView/delAllViews", null, { root: true });

          resolve();
        })
        .catch(error => {
          console.log('logout error: ', error)
          reject(error);
        });
    });
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit("SET_TOKEN", "");
      commit("SET_ROLES", []);
      removeToken();
      resolve();
    });
  },

  // dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    // TODO: 需要处理async
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      const token = role + "-token";

      commit("SET_TOKEN", token);
      setToken(token);

      const { roles } = await dispatch("getInfo");

      resetRouter();

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch("permission/generateRoutes", roles, {
        root: true
      });

      // dynamically add accessible routes
      router.addRoutes(accessRoutes);

      // // reset visited views and cached views
      // dispatch("tagsView/delAllViews", null, { root: true });

      resolve();
    });
  },

  clearRoles({ commit }) {
    return new Promise(resolve => {
      commit("SET_TOKEN", "");
      commit("SET_ROLES", []);
      removeToken();
      window.localStorage.removeItem("userInfo");
      resolve();
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
