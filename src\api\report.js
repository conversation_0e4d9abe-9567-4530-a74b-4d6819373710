import request from '@/utils/request'
import requestDownload from '@/utils/requestDownload'

/**
 * 报表-导出订单
 */
export function reportExport(data) {
  return requestDownload({
    url: '/report/export',
    method: 'post',
    data
  })
}

/**
 * 报表-获取配置信息
 */
export function getReportProperties() {
  return request({
    url: '/report/properties',
    method: 'post'
  })
}

/**
 * 报表-查询订单
 */
export function queryOrder(data) {
  return request({
    url: '/report/queryOrder',
    method: 'post',
    data
  })
}

// 查询对账账单
export function queryReconcliationOrder(data) {
  return request({
    url: '/payCheck/query',
    method: 'post',
    data
  })
}

// 导出对账账单excel
export function reportReconcliationOrder(data) {
  return requestDownload({
    url: '/payCheck/export',
    method: 'post',
    data
  })
}

//查询提现账户
export function querywithdrawalOrder(data) {
  return request({
    url: '/withdrawal/getAccounts',
    method: 'post',
    data
  })
}

//提现提交
export function querywithdrawalSubmit(data) {
  return request({
    url: '/withdrawal/accountWithdrawal',
    method: 'post',
    data
  })
}

//查询提现记录
export function querywithdrawalRecord(data) {
  return request({
    url: '/withdrawal/records',
    method: 'post',
    data
  })
}

/*
 *@functionName: autoCheckBill
 *@description: 自动对账列表查询
 *@author: caoshuwen
 *@date: 2021-1-11
 */
export function autoCheckBill(data) {
  return request({
    url: '/autoCheckBill/query',
    method: 'post',
    data
  })
}

/*
 *@functionName: reportAutoCheck
 *@description: 导出自动对账列表
 *@author: caoshuwen
 *@date: 2021-1-11
 */
export function reportAutoCheck(data) {
  return requestDownload({
    url: '/autoCheckBill/export',
    method: 'post',
    data
  })
}

/*
 *@functionName: tradeCheck
 *@description: 富民交易对账查询
 *@author: caoshuwen
 *@date: 2021-07-16
 */
export function tradeCheck(data) {
  return request({
    url: '/tradeCheck/query',
    method: 'post',
    data
  })
}

/*
 *@functionName: reportTradeCheck
 *@description: 导出富民交易对账
 *@author: caoshuwen
 *@date: 2021-07-16
 */
export function reportTradeCheck(data) {
  return requestDownload({
    url: '/tradeCheck/export',
    method: 'post',
    data
  })
}

/*
 *@functionName: payAmountReport
 *@description: 支付金额报表统计接口
 *@author: caoshuwen
 *@date: 2021-10-18
 */
export function payAmountReport(data) {
  return request({
    url: '/payAmountReport/query',
    method: 'post',
    data
  })
}

/*
 *@functionName: businessTypeList
 *@description: 业务线list
 *@author: caoshuwen
 *@date: 2021-10-18
 */
export function businessTypeList() {
  return request({
    url: '/common/getDict/payAmountReportBusinessType',
    method: 'get'
  })
}

/*
 *@functionName: businessOrderTypeList
 *@description: 业务订单类型list
 *@author: caoshuwen
 *@date: 2021-10-18
 */
export function businessOrderTypeList() {
  return request({
    url: '/common/getDict/payAmountReportBusinessOrderType',
    method: 'get'
  })
}

/*
 *@functionName: payChannelList
 *@description: 支付渠道list
 *@author: caoshuwen
 *@date: 2021-10-18
 */
export function payChannelList() {
  return request({
    url: '/common/getDict/payAmountReportChannelType',
    method: 'get'
  })
}

/**
 * 富民交易查询
 * @returns {AxiosPromise}
 */
export function queryFuminRecord(data) {
  return request({
    url: '/queryTrade/fuminRecord',
    method: 'post',
    data
  })
}

/**
 * 交易信息查询
 * @returns {AxiosPromise}
 */
export function queryTransactionInfo(data) {
  return request({
    url: '/queryTrade/queryTransactionInfo',
    method: 'post',
    data
  })
}

/**
 * 流水映射
 */
export function reportPayRecordMapExport(data) {
  return requestDownload({
    url: '/queryTrade/download',
    method: 'post',
    data
  })
}
