import request from '@/utils/request'

/**
 * 账户-查询账户信息
 */
export function accountQuery() {
  return request({
    url: '/account/query',
    method: 'post'
  })
}
/*获取账号权限列表*/
export function getUserList(data) {
  return request({
    url: '/user/getUser',
    method: 'post',
    data
  })
}
/*权限设置*/
export function updatePermission(data) {
  return request({
    url: '/permission/update',
    method: 'post',
    data
  })
}
/*账号设置*/
export function updateUser(data) {
  return request({
    url: '/user/edit',
    method: 'post',
    data
  })
}
/*获取权限接口*/
export function getUserPower(account) {
  return request({
    // url: `/user/getPermission/${account}`,
    // method: 'get'
    url: '/user/getPermission',
    method: 'post',
    data: { account }
  })
}

/*
 *@functionName: addUser
 *@params: data {userName,userAccount,depart,active}
 *@description: 新增账号
 *@author: zhanhui
 *@date: 2020-11-05 17:20:11
*/
export function addUser(data) {
  return request({
    url: `/user/add`,
    method: 'post',
    data
  })
}

/*
 *@functionName: getAllMenu
 *@description: 获取所有的菜单树
 *@author: zhanhui
 *@date: 2020-11-05 17:20:11
*/
export function getAllMenu() {
  return request({
    url: `/permission/getAllMenu`,
    method: 'get'
  })
}
