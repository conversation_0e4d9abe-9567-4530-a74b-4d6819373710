<template>
  <div class="finance-stagement mt10">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="交易时间" label-width="90px" prop="receiptTime">
          <el-date-picker
            v-model="formModel.receiptTime"
            type="daterange"
            :clearable="true"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="formItem[0].attrs['picker-options']"
            @change="validateFormRequiredItem"
          />
        </el-form-item>

        <el-form-item label="业务线" label-width="90px" prop="businessType">
          <ll-select v-model="formModel.businessType" :options="formItem[1].attrs['options']" />
        </el-form-item>

        <el-form-item label="业务订单类型" label-width="100px" prop="businessOrderType">
          <ll-select v-model="formModel.businessOrderType" :options="formItem[2].attrs['options']" />
        </el-form-item>

        <el-form-item label="支付渠道" label-width="90px" prop="payChannel">
          <ll-select v-model="formModel.payChannel" :options="formItem[3].attrs['options']" />
        </el-form-item>

        <el-form-item label=" " label-width="90px">
          <ll-button type="primary" @click="handleFormSubmit">查询</ll-button>
        </el-form-item>
      </el-form>

      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
      />
    </ll-list-page-layout>
  </div>
</template>

<script>
import { payAmountReport, businessTypeList, businessOrderTypeList, payChannelList } from '@/api/report'
import moment from 'moment'
const dataOption = [
  { desc: '宜块钱', label: '宜块钱', value: 'YKQ' },
  { desc: 'POP', label: 'POP', value: 'POP' },
  { desc: '智慧脸商城', label: '智慧脸商城', value: 'ZHL' },
  { desc: '药帮忙', label: '药帮忙', value: 'ec' }
]
export default {
  data() {
    return {
      typeindex: 0, //费用类型的下标
      searchFormKey: `search-form-key-${new Date().getTime()}`,
      formModel: {
        receiptTime: [
          moment()
            .subtract(1, 'months')
            .startOf('month')
            .startOf('hour')
            .valueOf(),
          moment()
            .subtract(1, 'months')
            .endOf('month')
            .startOf('day')
            .startOf('hour')
            .valueOf()
        ], // 交易时间
        businessType: '', // 业务场景
        businessOrderType: '', //业务订单类型
        payChannel: '', //支付渠道
      },
      formItem: [
        {
          label: '交易时间',
          prop: 'receiptTime',
          component: 'el-date-picker',
          attrs: {
            type: 'daterange',
            clearable: 'true',
            'range-separator': '至',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            'picker-options': {
              disabledDate: this.receiptTimeDisabledDate,
              onPick: this.receiptTimeOnPick
            }
          },
          width: '220px',
          error: ''
        },
        {
          label: '业务线',
          prop: 'businessType',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '业务订单类型',
          prop: 'businessOrderType',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '支付渠道',
          prop: 'payChannel',
          component: 'll-select',
          attrs: {
            options: []
          },
          width: '220px'
        }
      ],
      formRules: {
        receiptTime: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ]
      },
      receiptTimeOptionRange: '',
      tableColumns: [
        {
          prop: 'businessType',
          label: '业务线',
          'min-width': '120'
        },
        {
          prop: 'businessOrderType',
          label: '业务订单类型',
          'min-width': '150'
        },
        {
          prop: 'payChannel',
          label: '支付渠道',
          'min-width': '120'
        },
        {
          prop: 'amount',
          label: '金额',
          'min-width': '100'
        },
        {
          prop: 'countTime',
          label: '统计时间',
          'min-width': '95'
        }
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false
    }
  },
  created() {
    this.getBusinessTypeList()
    this.getBusinessOrderTypeList()
    this.getPayChannelList()
    this.accountQuery()
  },
  mounted() {},
  methods: {
    // 获取业务线list
    async getBusinessTypeList() {
        try {
            const res = await businessTypeList()
            this.formItem[1].attrs.options = res.result
            this.formItem[1].attrs.options.unshift({label:'全部', value: ''})
        } catch (error) {
            console.log(error);
        }
    },
    // 获取业务订单类型list
    async getBusinessOrderTypeList() {
        try {
            const res = await businessOrderTypeList()
            this.formItem[2].attrs.options = res.result
            this.formItem[2].attrs.options.unshift({label:'全部', value: ''})
        } catch (error) {
            console.log(error);
        }
    },
    // 获取支付渠道list
    async getPayChannelList() {
        try {
            const res = await payChannelList()
            this.formItem[3].attrs.options = res.result
            this.formItem[3].attrs.options.unshift({label:'全部', value: ''})
        } catch (error) {
            console.log(error);
        }
    },
    // 获取用户信息
    accountQuery() {
      let option = []
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      dataOption.forEach(p => {
        if (dataPermission.includes(p.value)) {
          option.push(p)
        }
      })
    },
    validateFormRequiredItem() {
      this.$refs.searchForm.validateField('receiptTime')
      this.$refs.searchForm.validateField('tradeNo')
      this.$refs.searchForm.validateField('businessOrderNo')
      this.$refs.searchForm.validateField('channelTransactionNo')
    },
    /**
     * 校验 交易时间、商户订单号、业务订单号、支付渠道交易单号，至少一项填写
     */
    validateRequiredItem(rule, value, callback) {
      let errorMsg = ''
      if (this.validateParamsAllEmpty()) {
        errorMsg = '请填写任意一项筛选项'
        callback(new Error(errorMsg))
      }
      callback()
    },

    validateParamsAllEmpty() {
      const {
        // 交易时间
        receiptTime
      } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      if (this.xyySaasValidator.isNull(beginDate) && this.xyySaasValidator.isNull(endDate)) {
        return false
      }
    },
    formatterTableData(tableData, pagination, totalCount) {
      console.log(tableData)
      let reportQueryOrderViewBos = tableData || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        item.serialNumber = serialNumber
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: totalCount || 0 }
      }

      return result
    },
    getFormParams(pagination) {
      const { receiptTime } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      let params = {
        businessType: this.formModel.businessType, //业务线
        businessOrderType: this.formModel.businessOrderType, //业务订单类型
        payChannel: this.formModel.payChannel,//支付渠道
        startTime: beginDate, // 交易时间 - 开始时间
        endTime: endDate, // 交易时间 - 结束时间
        pageNum: pagination ? pagination.currentPage : null,
        pageSize: pagination ? pagination.pageSize : null
      }
      delete params.receiptTime
      for (const key in params) {
        if (this.xyySaasValidator.isNull(params[key])) {
          delete params[key]
        }
      }

      return params
    },
    fetchData({ pagination }) {
      let dataPermission = this.$store.getters.dataPermission || JSON.parse(localStorage.getItem('userInfo')).dataPermission || ''
      if (dataPermission === '') {
        let obj = {
          list: [],
          pagination: { total: 0 }
        }
        return Promise.resolve(obj)
      }
      let params = this.getFormParams(pagination)
      return new Promise(resolve => {
        payAmountReport(params)
          .then(response => {
            let { data } = response
            let { totalCount } = response
            let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },

    receiptTimeDisabledDate(time) {
      // console.log("receiptTimeDisabledDate", moment(time));
      let receiptTimeOptionRange = this.receiptTimeOptionRange

      if (receiptTimeOptionRange) {
        // let secondNum = 60 * 60 * 24 * 3 * 1000;
        let startTime = receiptTimeOptionRange,
          currTime = moment(time),
          minTime = moment(startTime).subtract(1, 'months'),
          maxTime = moment(startTime).add(1, 'months')
        return currTime.isBefore(minTime) || currTime.isAfter(maxTime)
      } else {
        return false
      }
    },
    receiptTimeOnPick(time) {
      //当第一时间选中才设置禁用
      if (time.minDate && !time.maxDate) {
        this.receiptTimeOptionRange = time.minDate
      }
      if (time.maxDate) {
        this.receiptTimeOptionRange = null
      }
    },

    // 重新加载表
    refreshList() {
      this.$refs['dataGrid'].loadData()
    },

    // 查询
    handleFormSubmit() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.refreshList()
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
</style>
