<template>
  <div  class="finance-stagement mt10" style="">
    <ll-list-page-layout >
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="业务场景" label-width="90px" prop="businessIdType">
          <ll-select v-model="formModel.businessIdType" :options="formItem[0].attrs['options']" />
        </el-form-item>

        <el-form-item label="用户ID" label-width="90px" prop="businessId">
          <el-input v-model="formModel.businessId" placeholder="用户ID" />
        </el-form-item>

        <el-form-item label=" " label-width="90px">
          <ll-button type="primary" @click="handleFormSubmit">查询</ll-button>
        </el-form-item>
      </el-form>

      <div>
        <el-timeline >
          <el-timeline-item
            v-for="(item) in steps"
            :key="item.step"
            :timestamp="item.time">
            <label>{{item.desc}}</label> {{item.msg}}
          </el-timeline-item>
        </el-timeline>
      </div>
    </ll-list-page-layout>

  </div>
</template>

<script>
import { getDictMapQuery } from '@/api/common'
import { queryOpenAccountStatus } from '@/api/openAccount'
export default {
  data() {
    return {
      formModel: {
        businessIdType: 'ec_pop', // 业务场景
        businessId: '' //账户类型
      },
      formItem: [
          {
            label: '业务场景',
            prop: 'businessIdType',
            component: 'll-select',
            attrs: {
              options: []
            },
            width: '220px'
          }
        ],
      formRules: {
        businessId: [
          {
            validator: this.validateRequiredItem,
            trigger: 'blur'
          }
        ],
      },
      steps: []
    };
  },
  created() {
    getDictMapQuery("businessIdTypeMap", "map").then(response => {
      this.formItem[0].attrs.options = response.result;
    })
  },
  mounted() {},
  methods: {
    // 查询
    handleFormSubmit() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          var data = {'businessId': this.formModel.businessId,'businessIdType':this.formModel.businessIdType};
          queryOpenAccountStatus(data).then(response => {
            this.steps = response.result.step;
          }).catch(() => {
            this.steps = [];
          });
        } else {
          return false
        }
      })
    },
    validateRequiredItem(rule, value, callback) {
      console.log(rule,value,callback)
      let errorMsg = ''
      if (!this.validateParamsAllEmpty()) {
        errorMsg = '不能为空'
        callback(new Error(errorMsg))
      }
      callback()
    },
    validateParamsAllEmpty(){
      const {
        businessIdType,
        businessId
      } = this.formModel
      if(businessId == '' || businessIdType == '') {
        return false;
      }else {
        return true;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
</style>
