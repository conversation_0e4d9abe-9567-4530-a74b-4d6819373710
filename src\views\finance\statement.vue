<template>
  <div class="finance-stagement mt10" style="">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <!-- <ll-search-form
        ref="searchForm"
        key="searchFormKey"
        label-width="120px"
        slot="search-form"
        :model="formModel"
        :rules="formRules"
        :form-items="formItem"
        @form-item-value-change="validateRequiredItem"
        @submit="handleFormSubmit"
        @reset="handleFormSubmit"
      /> -->
      <el-form
        ref="searchForm"
        slot="search-form"
        class="statement-search-form"
        label-width="120px"
        :inline="true"
        :model="formModel"
        :rules="formRules"
      >
        <el-form-item label="交易时间" prop="receiptTime">
          <el-date-picker
            v-model="formModel.receiptTime"
            type="daterange"
            :clearable="true"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="formItem[0].attrs['picker-options']"
            @change="validateFormRequiredItem"
          />
        </el-form-item>

        <el-form-item label="业务场景" prop="businessOrderType">
          <ll-search-form-select v-model="formModel.businessOrderType" :options="formItem[1].attrs['options']" />
        </el-form-item>

        <el-form-item label="付款方名称" prop="payer">
          <el-input v-model="formModel.payer" placeholder="付款方名称" />
        </el-form-item>

        <el-form-item label="商户订单号" prop="tradeNo">
          <el-input v-model="formModel.tradeNo" placeholder="商户订单号" @blur="validateFormRequiredItem" />
        </el-form-item>

        <el-form-item label="业务订单号" prop="businessOrderNo">
          <el-input v-model="formModel.businessOrderNo" placeholder="业务订单号" @blur="validateFormRequiredItem" />
        </el-form-item>

        <el-form-item label="支付机构订单号" prop="channelTransactionNo">
          <el-input v-model="formModel.channelTransactionNo" placeholder="支付机构订单号" @blur="validateFormRequiredItem" />
        </el-form-item>

        <el-form-item label="类型" prop="orderType">
          <ll-select v-model="formModel.orderType" :options="formItem[6].attrs['options']" />
        </el-form-item>

        <el-form-item label="支付方式" prop="payMode">
          <ll-search-form-select v-model="formModel.payMode" :options="formItem[7].attrs['options']" />
        </el-form-item>

        <el-form-item label=" ">
          <!-- <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="primary" @click="onSubmit">查询</el-button> -->
          <ll-button type="primary" @click="handleFormSubmit">查询</ll-button>
          <ll-button type="text" @click="handleFormReset">重置</ll-button>
        </el-form-item>
      </el-form>

      <!-- 主要操作 -->
      <template slot="action-bar_left">
        <!-- <el-button type="primary">新增</el-button> -->
        <el-button @click="handleExportFile">导出Excel</el-button>
      </template>
      <!-- 次要操作 -->
      <template slot="action-bar_right">
        <!-- <el-button>筛选列</el-button> -->
        <!-- <el-button>导出Excel</el-button> -->
      </template>

      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
      />
    </ll-list-page-layout>
  </div>
</template>

<script>
import { queryOrder, reportExport } from '@/api/report'
import moment from 'moment'
import store from '@/store'
import { mapGetters } from 'vuex'
import { parseTime, download } from '@/utils/index.js'

export default {
  data() {
    return {
      searchFormKey: `search-form-key-${new Date().getTime()}`,
      formModel: {
        receiptTime: [
          moment()
            .subtract(1, 'months')
            .startOf('month')
            .startOf('hour')
            .valueOf(),
          moment()
            .subtract(1, 'months')
            .endOf('month')
            .startOf('day')
            .startOf('hour')
            .valueOf()
        ], // 交易时间
        businessOrderType: undefined, // 业务场景
        payer: '', // 付款方
        tradeNo: '', // 商户订单号
        businessOrderNo: '', // 业务订单号
        channelTransactionNo: '', // 支付渠道交易单号
        orderType: 'pay', // 订单类型
        payMode: undefined // 支付方式
      },
      formItem: [
        {
          label: '交易时间',
          prop: 'receiptTime',
          component: 'el-date-picker',
          attrs: {
            type: 'daterange',
            clearable: 'true',
            'range-separator': '至',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            'picker-options': {
              disabledDate: this.receiptTimeDisabledDate,
              onPick: this.receiptTimeOnPick
            }
          },
          width: '220px',
          error: ''
        },
        {
          label: '业务场景',
          prop: 'businessOrderType',
          component: 'll-search-form-select',
          attrs: {
            options: []
          },
          width: '220px'
        },
        {
          label: '付款方名称',
          prop: 'payer',
          component: 'el-input',
          attrs: {
            placeholder: '付款方名称'
          },
          width: '220px'
        },
        {
          label: '商户订单号',
          prop: 'tradeNo',
          component: 'el-input',
          attrs: {
            placeholder: '商户订单号'
          },
          width: '220px',
          error: ''
        },
        {
          label: '业务订单号',
          prop: 'businessOrderNo',
          component: 'el-input',
          attrs: {
            placeholder: '业务订单号'
          },
          width: '220px',
          error: ''
        },
        {
          label: '支付机构订单号',
          prop: 'channelTransactionNo',
          component: 'el-input',
          attrs: {
            placeholder: '支付机构订单号'
          },
          width: '220px',
          error: ''
        },
        {
          label: '类型',
          prop: 'orderType',
          component: 'll-select',
          attrs: {
            // orderType
            clearable: false,
            noAddAll: true,
            options: []
          },
          width: '220px'
        },
        {
          label: '支付方式',
          prop: 'payMode',
          component: 'll-search-form-select',
          attrs: {
            options: []
          },
          width: '220px'
        }
      ],
      formRules: {
        receiptTime: [
          {
            validator: this.validateRequiredItem,
            trigger: 'change'
          }
        ],
        payer: [
          {
            type: 'string',
            max: 60,
            message: '付款方名称长度在60字符以内',
            trigger: 'blur'
          }
        ],
        tradeNo: [
          {
            type: 'string',
            max: 60,
            message: '商户订单号长度在60字符以内',
            trigger: 'blur'
          },
          {
            validator: this.validateRequiredItem,
            trigger: 'blur'
          }
        ],
        businessOrderNo: [
          {
            type: 'string',
            max: 60,
            message: '业务订单号长度在60字符以内',
            trigger: 'blur'
          },
          {
            validator: this.validateRequiredItem,
            trigger: 'blur'
          }
        ],
        channelTransactionNo: [
          {
            type: 'string',
            max: 60,
            message: '支付机构订单号长度在60字符以内',
            trigger: 'blur'
          },
          {
            validator: this.validateRequiredItem,
            trigger: 'blur'
          }
        ]
      },
      receiptTimeOptionRange: '',
      tableColumns: [
        {
          // type: "index", // selection, index  serial number
          prop: 'serialNumber',
          label: '序号',
          width: '50'
        },
        {
          prop: 'payTime',
          label: '交易时间',
          width: '180'
        },
        {
          prop: 'businessOrderType',
          label: '业务场景',
          width: '180'
        },
        {
          prop: 'payer',
          label: '付款方名称',
          width: '180'
        },
        {
          prop: 'payNo',
          label: '商户订单号',
          width: '180'
        },
        {
          prop: 'businessOrderNo',
          label: '业务订单号',
          width: '180'
        },
        {
          prop: 'channelTransactionNo',
          label: '支付机构订单号',
          width: '180'
        },
        {
          prop: 'orderType',
          label: '类型',
          width: '180'
        },
        {
          prop: 'payMode',
          label: '支付方式',
          width: '180'
        },
        {
          prop: 'amount',
          label: '订单金额',
          width: '180'
        }
      ],
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false
    }
  },
  async beforeRouteEnter(to, from, next) {
    console.log('beforeRouteEnter------------------------')
    await store.dispatch('dictionary/reportProperties')
    console.log('next------------------------')
    next()
  },
  computed: {
    ...mapGetters(['dictionaryReport'])
  },
  created() {
    console.log('dictionaryReport-------------------', this.dictionaryReport)
    let { businessOrderTypes = [], orderType = [], payModes = [] } = this.dictionaryReport

    // 业务场景
    // this.formItem[1].attrs.options = businessOrderTypes;
    this.$set(this.formItem[1].attrs, 'options', businessOrderTypes)
    // 类型
    // this.formItem[6].attrs.options = orderType;
    this.$set(this.formItem[6].attrs, 'options', orderType)
    // 支付方式
    // this.formItem[7].attrs.options = payModes;
    this.$set(this.formItem[7].attrs, 'options', payModes)
    // this.$nextTick(function() {
    // });
  },
  mounted() {
    // console.log("mounted------------------------", this.xyySaasValidator);
    // console.log(this.$refs);
  },
  methods: {
    getFormItemByProp(prop) {
      for (let i = 0; i < this.formItem.length; i++) {
        const item = this.formItem[i]
        if (item.prop == prop) {
          return item
        }
      }
      return null
    },
    validateFormRequiredItem() {
      console.log('validateFormRequiredItem ================')
      this.$refs.searchForm.validateField('receiptTime')
      this.$refs.searchForm.validateField('tradeNo')
      this.$refs.searchForm.validateField('businessOrderNo')
      this.$refs.searchForm.validateField('channelTransactionNo')
    },
    /**
     * 校验 交易时间、商户订单号、业务订单号、支付渠道交易单号，至少一项填写
     */
    validateRequiredItem(rule, value, callback) {
      console.log('validateRequiredItem change value: ', value)
      let errorMsg = ''
      if (this.validateParamsAllEmpty()) {
        errorMsg = '请填写任意一项筛选项'
        callback(new Error(errorMsg))
      }
      callback()
    },

    validateParamsAllEmpty() {
      const {
        // 交易时间
        receiptTime,
        // 商户订单号
        tradeNo,
        // 业务订单号
        businessOrderNo,
        // 支付渠道交易单号
        channelTransactionNo
      } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      if (this.xyySaasValidator.isNull(beginDate) && this.xyySaasValidator.isNull(endDate)) {
        if (
          this.xyySaasValidator.isNull(tradeNo) &&
          this.xyySaasValidator.isNull(businessOrderNo) &&
          this.xyySaasValidator.isNull(channelTransactionNo)
        ) {
          return true
        }
      }
      return false
    },
    formatterTableData(tableData, pagination) {
      let reportQueryOrderViewBos = tableData.reportQueryOrderViewBos || []
      const { currentPage, pageSize } = pagination

      reportQueryOrderViewBos.map((item, index) => {
        // let payTime = parseTime(item.payTime);
        let serialNumber = (currentPage - 1) * pageSize + index + 1
        // let amount = fmoney({
        //   number: item.amount / 100,
        //   n: 2
        // });

        item.serialNumber = serialNumber
        // item.payTime = payTime;
        // item.amount = amount;
        // this.$set(item, "serialNumber", serialNumber);
        return item
      })

      let result = {
        list: reportQueryOrderViewBos,
        pagination: { total: tableData.dataCount || 0 }
      }

      return result
    },
    getFormParams(pagination) {
      const { receiptTime } = this.formModel
      const beginDate = receiptTime && receiptTime[0] ? moment(this.formModel.receiptTime[0]).valueOf() : null,
        endDate = receiptTime && receiptTime[1] ? moment(this.formModel.receiptTime[1]).valueOf() : null

      let params = Object.assign({}, this.formModel, {
        beginDate: beginDate, // 交易时间 - 开始时间
        endDate: endDate, // 交易时间 - 结束时间
        page: pagination ? pagination.currentPage : null,
        pageSize: pagination ? pagination.pageSize : null
      })
      delete params.receiptTime
      for (const key in params) {
        if (this.xyySaasValidator.isNull(params[key])) {
          delete params[key]
        }
      }

      return params
    },
    fetchData({ pagination }) {
      console.log('LOG: fetchData -> pagination', pagination)
      // console.log("LOG: fetchData -> formModel", this.formModel);
      // const params = {
      //   ...this.formModel,
      //   ...{ page: pagination.currentPage, pageSize: pagination.pageSize }
      // };

      let params = this.getFormParams(pagination)
      // const { result } = await queryOrder(params);
      return new Promise(resolve => {
        queryOrder(params)
          .then(response => {
            let { result } = response
            let tableData = this.formatterTableData(result, pagination)
            console.log('4444')
            console.log(tableData)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },

    receiptTimeDisabledDate(time) {
      // console.log("receiptTimeDisabledDate", moment(time));
      let receiptTimeOptionRange = this.receiptTimeOptionRange

      if (receiptTimeOptionRange) {
        // let secondNum = 60 * 60 * 24 * 3 * 1000;
        let startTime = receiptTimeOptionRange,
          currTime = moment(time),
          minTime = moment(startTime).subtract(1, 'months'),
          maxTime = moment(startTime).add(1, 'months')
        return currTime.isBefore(minTime) || currTime.isAfter(maxTime)
      } else {
        return false
      }
    },
    receiptTimeOnPick(time) {
      //当第一时间选中才设置禁用
      if (time.minDate && !time.maxDate) {
        this.receiptTimeOptionRange = time.minDate
      }
      if (time.maxDate) {
        this.receiptTimeOptionRange = null
      }
    },

    handleReceiptTimeChange(val) {
      console.log('handleReceiptTimeChange ----- ', val)
    },

    refreshList() {
      this.$refs['dataGrid'].loadData()
    },

    handleFormSubmit() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.refreshList()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleFormReset() {
      this.$refs.searchForm.resetFields()
      this.handleFormSubmit()
    },

    handleExportFile() {
      this.fullscreenLoading = true
      let exportParams = this.getFormParams()
      const exportName = '财务报表'
      reportExport(exportParams)
        .then(response => {
          console.log('export response: ', response)
          let result = response.data
          const dateStr = parseTime(new Date(), '{y}{m}{d}')
          const defExportName = `${exportName}-${dateStr}.xlsx`
          const fileExportName =
            response && response.headers && response.headers['export-filename']
              ? decodeURIComponent(response.headers['export-filename'])
              : defExportName
          download(result, fileExportName)
        })
        .finally(() => {
          this.fullscreenLoading = false
        })
    }
  }
}
</script>

<style lang="scss">
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 340px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}

// .finance-stagement {
//   .formItemOrderType {
//     .ll-search-form-select {
//       .el-input {
//         .el-icon-circle-close {
//           display: none;
//         }

//         .el-icon-arrow-up {
//           display: block !important;
//         }
//       }
//     }
//   }
// }
// .ll-layout-list-page__search-form {
//   flex: auto
// }
// .ll-layout-list-page__main-content {
//   flex: auto
// }
// .el-table__body-wrapper {
//   height: auto !important;
// }
// .ll-data-grid-table{
//   height: calc(100% - 1px);
// }
</style>

<style lang="scss" scoped>
.finance-stagement {
  // border-top: 1px solid #dcdfe6;
  height: calc(100% - 10px);
  // height: 800px;
  // height: 100%;
}
</style>
