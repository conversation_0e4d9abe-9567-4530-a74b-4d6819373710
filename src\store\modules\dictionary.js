/* eslint-disable prettier/prettier */
import { getReportProperties } from "@/api/report";
import xyySaasValidator from '@xyy-saas/validator';

const state = {
  report: null
};

const mutations = {
  SET_DICTIONARY_REPORT: (state, report) => {
    console.log('SET_DICTIONARY_REPORT: ', report)
    state.report = report;
  }
};

const actions = {

  // 报表-获取配置信息
  reportProperties({ commit }) {
    return new Promise((resolve, reject) => {
      if(xyySaasValidator.isNull(state.report)){
        getReportProperties()
          .then(response => {
            const { result } = response;
            let {businessOrderTypes=[], orderType=[], payModes=[]} = result;
            for (let i = 0; i < businessOrderTypes.length; i++) {
              const businessOrderTypesItem = businessOrderTypes[i];
              businessOrderTypesItem.label = businessOrderTypesItem.desc || "";
            }
            for (let i = 0; i < orderType.length; i++) {
              const orderTypeItem = orderType[i];
              orderTypeItem.label = orderTypeItem.desc || "";
            }
            for (let i = 0; i < payModes.length; i++) {
              const payModesItem = payModes[i];
              payModesItem.label = payModesItem.desc || "";
            }
            commit("SET_DICTIONARY_REPORT", result);
            resolve();
          })
          .catch(error => {
            console.log('error: ', error)
            reject(error);
          });
      }else{
        resolve();
      }
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
