<template>
  <div class="finance-stagement mt10">
    <ll-list-page-layout v-loading.lock="fullscreenLoading" element-loading-text="正在导出数据....">
      <el-form ref="searchForm" slot="search-form" class="statement-search-form" label-width="120px" :inline="true" :model="formModel">
        <!-- :rules="formRules" -->
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="支付母单号" label-width="90px" prop="parentPayNo">
              <el-input v-model.trim="formModel.parentPayNo" placeholder="请输入支付母单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商城母单号" label-width="90px" prop="bizParentOrderNo">
              <el-input v-model.trim="formModel.bizParentOrderNo" placeholder="请输入商城母单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付子单号" label-width="90px" prop="subPayNo">
              <el-input v-model.trim="formModel.subPayNo" placeholder="请输入支付子单号" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商城子单号" label-width="90px" prop="bizOrderNo">
              <el-input v-model.trim="formModel.bizOrderNo" placeholder="请输入商城子单号" class="samewidth" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="商户编码" label-width="90px" prop="orgId">
              <el-input v-model.trim="formModel.orgId" placeholder="请输入商户编码" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商户名称" label-width="90px" prop="orgName">
              <el-input v-model.trim="formModel.orgName" placeholder="请输入商户名称" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户编码" label-width="90px" prop="payer">
              <el-input v-model.trim="formModel.payer" placeholder="请输入客户编码" class="samewidth" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称" label-width="90px" prop="payerName">
              <el-input v-model.trim="formModel.payerName" placeholder="请输入客户名称" class="samewidth" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="支付时间" label-width="90px">
              <el-date-picker
                v-model="formModel.settleFinishTime"
                type="datetimerange"
                :range-separator="'至'"
                :default-time="['00:00:00','23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="validateFormRequiredItem"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付渠道" label-width="90px" prop="payChannel">
              <el-select filterable v-model="formModel.payChannel" placeholder="请选择">
                <el-option label="全部" value="" />
                <el-option v-for="(item,index) in channelList" :key="index" :label="item.value" :value="item.key" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div>
              <ll-button @click="handleExportFile">导出Excel</ll-button>
              <ll-button @click="handleResetForm">重置</ll-button>
              <ll-button type="primary" @click="handleGetList">查询</ll-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <ll-data-grid
        ref="dataGrid"
        :pagination-page-size.sync="pagination.pageSize"
        :pagination-page-sizes="pagination.sizes"
        :table-columns="tableColumns"
        :async-fetch-data-fun="fetchData"
      >
        <!-- <template #payDay="{record}">
          <span>{{ (record.payDay) ? `${moment(record.payDay).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #curRepayDate="{record}">
          <span>{{ (record.curRepayDate) ? `${moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #actRepayDate="{record}">
          <span>{{ (record.actRepayDate) ? `${moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss')}` : '--' }}</span>
        </template>
        <template #payoffflag="{record}">
          <span>{{ {1:'已结清',0:'未结清'}[record.payoffflag] }}</span>
        </template> -->
      </ll-data-grid>
    </ll-list-page-layout>
  </div>
</template>

<script>
import { getMergePayList, getChannelEnum, exportMergePay } from '@/api/mergePay.js'
// 引入处理时间戳
import moment from 'moment'
import { download } from '@/utils/index.js'

export default {
  data() {
    return {
      payDay: [],
      formModel: {
        parentPayNo: '',
        bizParentOrderNo: '',
        subPayNo: '',
        bizOrderNo: '',
        orgId: '',
        orgName: '',
        payer: '',
        payerName: '',
        payStartTime: '',
        payEndTime: '',
        payMode: '',
        payChannel: '',
        settleFinishTime: [],
      },
      tableColumns: [
        {
          prop: 'payChannelStr',
          label: '支付渠道',
          'min-width': '120',
          // formatter: record => (record.payDay ? moment(record.payDay).format('YYYY-MM-DD HH:mm:ss') : '--')
        },
        {
          prop: 'parentPayNo',
          label: '支付母单号',
          'min-width': '120'
        },
        {
          prop: 'bizParentOrderNo',
          label: '商城母单号',
          'min-width': '120'
        },
        {
          prop: 'parentPayAmountToYuan',
          label: '母单支付金额(元)',
          'min-width': '120',
          // formatter: record => (record.payDay ? moment(record.payDay).format('YYYY-MM-DD HH:mm:ss') : '--')
        },
        {
          prop: 'subPayNo',
          label: '支付子单号',
          'min-width': '120'
        },
        {
          prop: 'bizOrderNo',
          label: '商城子单号',
          'min-width': '120'
        },
        {
          prop: 'amountToYuan',
          label: '子单支付金额(元)',
          'min-width': '120'
        },
        {
          prop: 'ctimeStr',
          label: '支付时间',
          'min-width': '120'
        },
        {
          prop: 'orgId',
          label: '商户编码',
          'min-width': '95'
        },
        {
          prop: 'orgName',
          label: '商户名称',
          'min-width': '95'
        },
        {
          prop: 'payer',
          label: '客户编码',
          'min-width': '95',
          // formatter: record => (record.curRepayDate ? moment(record.curRepayDate).format('YYYY-MM-DD HH:mm:ss') : '--')
        },
        {
          prop: 'payerName',
          label: '客户名称',
          'min-width': '95',
          // formatter: record => (record.actRepayDate ? moment(record.actRepayDate).format('YYYY-MM-DD HH:mm:ss') : '--')
        }
      ],
      channelList: [], // 支付渠道列表
      pagination: {
        currentPage: 1,
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      fullscreenLoading: false
    }
  },
  created() {
    // this.payDay = [this.lastMonthFirstDay(), moment().subtract(1, 'days').format('YYYY-MM-DD')]
    this.payDay = this.getTodayRange()
    this.getChannelList()
    this.formModel.settleFinishTime = this.payDay
    this.formModel.payStartTime = this.payDay[0]
    this.formModel.payEndTime = this.payDay[1]
    console.log(this.payDay,'payday');
    
  },
  mounted() {},
  methods: {
    lastMonthFirstDay() {
      return moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD')
    },
    getTodayRange() {
      const today = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD')
      const today1 = moment().subtract(1, 'days').format('YYYY-MM-DD')
      return [`${today} 00:00:00`, `${today1} 23:59:59`]
    },
    // 获取支付渠道
    getChannelList() {
      getChannelEnum().then(res => {
        this.channelList = res.result
      })
    },
    // 订单支付时间
    validateFormRequiredItem(val) {      
      if (val) {
        this.formModel.payStartTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.formModel.payEndTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.formModel.payStartTime = ''
        this.formModel.payEndTime = ''
      }
    },
    handleGetList() {
      this.$refs['dataGrid'].loadData()
    },
    fetchData({pagination}) {
      console.log(pagination)
      const { pageSize, currentPage } = pagination
      const params = Object.assign({ pageSize: pageSize, page: currentPage }, this.formModel)
      return new Promise(resolve => {
        getMergePayList(params)
          .then(response => {
            let { list, pages, total } = response.result
            let tableData = {
              list: list,
              pagination: {
                pageSize: pages,
                total: total
              }
            }
            
            // let tableData = this.formatterTableData(data, pagination, totalCount)
            resolve(tableData)
          })
          .catch(() => {
            resolve({
              list: [],
              pagination: { pageSize: 10, total: 0 }
            })
          })
      })
    },
    handleResetForm() {
      this.$refs.searchForm.resetFields()
      this.payDay = this.getTodayRange()
      this.formModel.settleFinishTime = this.payDay
      this.formModel.payStartTime = this.payDay[0]
      this.formModel.payEndTime = this.payDay[1]
      this.pagination = {
        currentPage: 1,
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      }
      
      this.handleGetList()
    },
    // 导出excel
    handleExportFile() {
      if (moment(this.formModel.payEndTime).diff(moment(this.formModel.payStartTime), 'days') > 31) {
        this.$message.warning('只允许导出处理时间区间为31天的数据')
        return false
      }
      const params = JSON.parse(JSON.stringify(this.formModel))
      delete params.settleFinishTime
      this.fullscreenLoading = true
      console.log(params,'params');
      
      exportMergePay(params)
        .then(res => {
          console.log(res,'resres');
          
          const result = res.data
          const fileExportName =
            res && res.headers && res.headers['export-filename'] ? decodeURIComponent(res.headers['export-filename']) : '合并支付交易数据.csv'
          download(result, fileExportName)
        })
        .finally(() => {
          this.fullscreenLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.finance-stagement {
  .statement-search-form {
    .el-form-item {
      width: 320px;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
        .el-range-editor {
          width: 220px;
        }
        width: 220px;
      }
    }
  }
}
.finance-stagement {
  height: calc(100% - 10px);
}
.samewidth {
  width: 220px;
}
.checkboxCol {
  padding-left: 30px !important;
  padding-top: 10px;
}
::v-deep .checkboxCol .el-checkbox__label {
  font-weight: 600;
}
</style>