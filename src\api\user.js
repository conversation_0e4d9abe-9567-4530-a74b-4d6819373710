import request from "@/utils/request";

export function userLogin(data) {
  return request({
    url: "/vue-admin-template/user/login",
    method: "post",
    data
  });
}

export function getUserInfo(token) {
  return request({
    url: "/vue-admin-template/user/info",
    method: "get",
    params: { token }
  });
}

export function userLogout() {
  return request({
    url: "/logout/default",
    method: "post"
  });
}
