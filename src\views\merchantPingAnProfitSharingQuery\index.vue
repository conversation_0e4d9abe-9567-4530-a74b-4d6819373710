<template>
  <div class="top_fth mt10" style="">
    <div class="top">
      <el-form label-width="120px" :inline="true">
        <el-form-item label="商户编码" prop="userId">
          <el-input v-model="userId" />
        </el-form-item>
        <el-form-item label="">
          <ll-button type="primary" @click="queryAccount">查询</ll-button>
        </el-form-item>
      </el-form>
      <div class="line-1">
        <div class="line-2">
          <span>平安冻结金额: {{ frozenAmount }}</span>
          <ll-button class="line-btn" type="primary" @click="matching">匹配未分账订单</ll-button>
        </div>
        <div class="line-2">
          <span>平安可提现金额: {{ withdrawableBalance }}</span>
          <ll-button class="line-btn" type="primary" @click="withdrawal">提现至平台佣金户</ll-button>
        </div>
      </div>
    </div>

    <div class="tableSty">
      <el-table :data="tableData" stripe border v-loading="loading" style="max-height: calc(100%);overflow-y: auto;">
        <el-table-column prop="businessOrderNo" label="订单编号" width="140"></el-table-column>
        <el-table-column prop="payNo" label="支付单号" width="140"></el-table-column>
        <el-table-column prop="shareState" label="分润状态">
          <template slot-scope="scope">
            <span>{{ { 1: "待分润", 16: "分润失败", 8: "分润成功" }[scope.row.shareState] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="finishSettleAmount" label="已分润金额"></el-table-column>
        <el-table-column prop="unFinishSettleAmount" label="未分润金额"></el-table-column>
        <el-table-column prop="surplusSettleAmount" label="剩余可分润金额"></el-table-column>
        <el-table-column label="手动输入分账金额" width="140">
          <template slot-scope="scope">
            <el-input v-model="scope.row.amount" type="number" size="small" @change="changeNum(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="date" label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button size="small" type="text" @click="separateAccounts(scope.row)">分账</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 提现弹框 -->
    <el-dialog
      title="提现至平台佣金户"
      :visible.sync="withdrawalDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="withdrawalForm" ref="withdrawalForm" label-width="80px">
        <el-form-item label="提现金额" prop="amount">
          <el-input 
            v-model="withdrawalForm.amount" 
            type="number" 
            :max="withdrawableBalance"
            placeholder="请输入提现金额">
            <template slot="append">元</template>
          </el-input>
          <div class="amount-tip">可提现金额：{{ withdrawableBalance }}元</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="withdrawalForm.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="withdrawalDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitWithdrawal">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryFrozenAmount, queryBalance, queryUnsettle, accountTransfer, settle } from '@/api/merchantPingAnProfitSharingQuery'

export default {
  data() {
    return {
      userId: null, // 商户编码
      frozenAmount: 0, // 平安冻结金额
      withdrawableBalance: 0, // 可提现余额
      withdrawalDialogVisible: false, // 提现弹框显示状态
      withdrawalForm: {
        amount: '',
        remark: ''
      },
      pagination: {
        pageSize: 10,
        sizes: [10, 20, 30, 50, 100]
      },
      tableData: [
        // { surplusSettleAmount: 100, amount: null },
      ],
      formData: {},
      loading: false, //表格加载中
    }
  },
  methods: {
    queryAccount() { // 商户平安分润查询
      this.tableData = []
      if(!this.userId) {
        this.$message.warning('请输入商户编码')
        return
      }
      queryFrozenAmount({
        userId: this.userId,
      }).then(res => {
        if (res.code == 1000) {
          this.frozenAmount = res.result
        }
      })
      queryBalance({
        userId: this.userId,
      }).then(res => {
        if (res.code == 1000) {
          this.withdrawableBalance = res.result
        }
      })
    },
    matching() { // 匹配未分账订单
      if(this.frozenAmount <= 0) {
        this.$message.warning('平安冻结金额不足')
        return
      }
      this.loading = true
      queryUnsettle({
        userId: this.userId,
      }).then(res => {
        if (res.code == 1000) {
          this.tableData = res.result.map(item => {
            item.amount = null
            return item
          })
        }
      }).finally(() => {
        this.loading = false
      })
    },
    withdrawal() { // 提现至平台佣金户
      // if(this.withdrawableBalance <= 0) {
      //   this.$message.warning('可提现金额不足')
      //   return
      // }
      this.withdrawalForm.amount = this.withdrawableBalance
      this.withdrawalForm.remark = ''
      this.withdrawalDialogVisible = true
    },
    submitWithdrawal() {
      if (!this.withdrawalForm.amount) {
        this.$message.warning('请输入提现金额')
        return
      }
      if (parseFloat(this.withdrawalForm.amount) <= 0 || parseFloat(this.withdrawalForm.amount) > this.withdrawableBalance) {
        this.$message.warning('提现金额必须大于0且不能超过可提现金额')
        return
      }
      accountTransfer({
        userId: this.userId,
        amount: parseFloat(this.withdrawalForm.amount),
        remark: this.withdrawalForm.remark
      }).then(res => {
        if (res.code == 1000) {
          this.$message.success('提现成功')
          this.withdrawalDialogVisible = false
          this.queryAccount() // 重新查询平安分润信息
        } else {
          // this.$message.error('提现失败')
          this.$message.error(res.msg || '提现失败')
        }
      })
    },
    separateAccounts(row) {
      if (row.amount === null) {
        this.$message.warning({ message: '请输入分账金额', offset: 200 })
        return
      }
      settle({
        payNo: row.payNo,
        amount: row.amount,
      }).then(res => {
        // console.log('手动分账返回', res)
        if(res.code == 1000){
          this.$message.success({ message: res.msg, offset: 200 })
        }
      })
    }, // 手动分账
    changeNum(value) {
      function resetAmount() {
        value.amount = null
      }
      function setAmount() {
        value.amount = tempAmount
      }
      const tempAmount = parseFloat(value.amount + '')
      if (isNaN(tempAmount)) {
        resetAmount()
        return;
      }
      if (tempAmount <= 0 || tempAmount - value.surplusSettleAmount > 0) {
        this.$message.warning({ message: '输入的分账金额应大于0, 且小于剩余可分润金额', offset: 200 })
        resetAmount()
        return;
      }

      const regex = /^\d+(\.\d{1,2})?$/
      if (!regex.test(tempAmount)) {
        resetAmount()
        this.$message.warning({ message: '请检查输入格式，最多只能输入两位小数', offset: 200 })
      } else setAmount()
    }, // 手动输入分账金额校验
  }
}
</script>

<style lang='scss' scoped>
.top_fth {
  height: calc(100% - 10px);
  display: flex;
  flex-direction: column;
  // border: 1px solid red;
  // background-color: #fff;
}

.top {
  background-color: #fff;
  box-sizing: border-box;
  padding: 10px;
  width: 100%;
}

.line-1 {
  display: flex;
  padding: 4px 40px;
  // width: 100%;
  // border: 1px solid red;
}

.line-2 {
  display: flex;
  margin: 4px 10px;
  align-items: center;
}

.line-btn {
  margin-left: 20px;
  margin-right: 40px;
}

.tableSty {
  background-color: #fff;
  box-sizing: border-box;
  padding: 10px;
  width: 100%;
  height: calc(100% - 200px);
  flex: 1;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.amount-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

:deep(.el-dialog__body) {
  padding: 20px 40px;
}
</style>